import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import { Label } from '@/components/ui/label';
import { Text } from '@/components/ui/text';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { SelectionGroup, type SelectionOption } from '@/components/selection-group';

import { useThemeColor } from '@/hooks/useThemeColor';
import { useAppStore } from '@/lib/store';
import { OutboundProtocols, OutboundConfig } from '@/panels/3x-ui/types';
import { ThreeXUIConfig } from '@/lib/types';
import { smartFetch } from '@/lib/utils';
import { getThreeXUIXrayConfig } from '@/panels/3x-ui/utils';
import { router, useLocalSearchParams, useNavigation } from 'expo-router';
import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { SafeAreaView, ScrollView, StyleSheet, View, Alert, Platform, TouchableOpacity, Modal } from 'react-native';
import { Edit, Trash2, PlusCircle, Link } from 'lucide-react-native';
import * as ClipboardAPI from 'expo-clipboard';
import { XUIConfigConverter } from '@/panels/3x-ui/utils';
import { FullWindowOverlay } from "react-native-screens";
import { BottomSheetModal, BottomSheetScrollView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';

import { merge } from 'lodash-es';

const WindowOverlay = Platform.OS === "ios" ? FullWindowOverlay : React.Fragment as any

export default function OutboundConfigScreen() {
  const { configId, index, link } = useLocalSearchParams<{
    configId: string;
    index?: string;
    link?: string;
  }>();
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');
  const borderColor = useThemeColor({}, 'border');
  const { getServerConfig, configs } = useAppStore();
  const navigation = useNavigation();

  // 表单状态
  const [tag, setTag] = useState('');
  const [protocol, setProtocol] = useState<string>(OutboundProtocols.FREEDOM);
  const [sendThrough, setSendThrough] = useState('');

  // Mux 设置
  const [muxEnabled, setMuxEnabled] = useState(false);
  const [muxConcurrency, setMuxConcurrency] = useState('8');
  const [muxXudpConcurrency, setMuxXudpConcurrency] = useState('16');
  const [muxXudpProxyUDP443, setMuxXudpProxyUDP443] = useState('reject');

  // 代理设置
  const [proxyTag, setProxyTag] = useState('');

  // Freedom 设置
  const [domainStrategy, setDomainStrategy] = useState('AsIs');
  const [redirect, setRedirect] = useState('');

  // Fragment 设置
  const [fragmentEnabled, setFragmentEnabled] = useState(false);
  const [fragmentPackets, setFragmentPackets] = useState('tlshello');
  const [fragmentLength, setFragmentLength] = useState('100-200');
  const [fragmentInterval, setFragmentInterval] = useState('10-20');

  // Noises 设置
  const [noisesEnabled, setNoisesEnabled] = useState(false);
  const [noises, setNoises] = useState<any[]>([]);

  // ProxyProtocol 设置
  const [proxyProtocol, setProxyProtocol] = useState('0');

  // Noise 编辑状态
  const [showNoiseDialog, setShowNoiseDialog] = useState(false);
  const [editingNoise, setEditingNoise] = useState<any | null>(null);
  const [noiseType, setNoiseType] = useState('base64');
  const [noisePacket, setNoisePacket] = useState('7nQBAAABAAAAAAAABnQtcmluZwZtc2VkZ2UDbmV0AAABAAE=');
  const [noiseDelay, setNoiseDelay] = useState('10-16');

  // Blackhole 设置
  const [blackholeResponseType, setBlackholeResponseType] = useState('none');

  // DNS 设置
  const [dnsNetwork, setDnsNetwork] = useState('tcp');
  const [dnsAddress, setDnsAddress] = useState('*******');
  const [dnsPort, setDnsPort] = useState('53');
  const [dnsNonIPQuery, setDnsNonIPQuery] = useState('drop');

  // HTTP 设置
  const [httpServerAddress, setHttpServerAddress] = useState('');
  const [httpServerPort, setHttpServerPort] = useState('');
  const [httpUsername, setHttpUsername] = useState('');
  const [httpPassword, setHttpPassword] = useState('');

  // SOCKS 设置
  const [socksServerAddress, setSocksServerAddress] = useState('');
  const [socksServerPort, setSocksServerPort] = useState('');
  const [socksUsername, setSocksUsername] = useState('');
  const [socksPassword, setSocksPassword] = useState('');

  // Shadowsocks 设置
  const [ssServerAddress, setSsServerAddress] = useState('');
  const [ssServerPort, setSsServerPort] = useState('');
  const [ssMethod, setSsMethod] = useState('aes-256-gcm');
  const [ssPassword, setSsPassword] = useState('');
  const [ssUot, setSsUot] = useState(false);
  const [ssUotVersion, setSsUotVersion] = useState('0');

  // VLESS 设置
  const [vlessServerAddress, setVlessServerAddress] = useState('');
  const [vlessServerPort, setVlessServerPort] = useState('');
  const [vlessUserId, setVlessUserId] = useState('');
  const [vlessFlow, setVlessFlow] = useState('');

  // VMess 设置
  const [vmessServerAddress, setVmessServerAddress] = useState('');
  const [vmessServerPort, setVmessServerPort] = useState('');
  const [vmessUserId, setVmessUserId] = useState('');
  const [vmessSecurity, setVmessSecurity] = useState('auto');

  // Trojan 设置
  const [trojanServerAddress, setTrojanServerAddress] = useState('');
  const [trojanServerPort, setTrojanServerPort] = useState('');
  const [trojanPassword, setTrojanPassword] = useState('');

  // Wireguard 设置
  const [wgSecretKey, setWgSecretKey] = useState('');
  const [wgEndpoint, setWgEndpoint] = useState('');
  const [wgPublicKey, setWgPublicKey] = useState('');
  const [wgMtu, setWgMtu] = useState('1420');
  const [wgAddress, setWgAddress] = useState('');
  const [wgNoKernelTun, setWgNoKernelTun] = useState(false);
  const [wgReserved, setWgReserved] = useState('');
  const [wgWorkers, setWgWorkers] = useState('');
  const [wgDomainStrategy, setWgDomainStrategy] = useState('ForceIP');

  // 传输设置状态
  const [transportType, setTransportType] = useState('tcp');

  // TCP 设置
  const [tcpHeaderType, setTcpHeaderType] = useState('none');
  const [tcpHttpRequestVersion, setTcpHttpRequestVersion] = useState('1.1');
  const [tcpHttpRequestMethod, setTcpHttpRequestMethod] = useState('GET');
  const [tcpHttpPaths, setTcpHttpPaths] = useState(['/']);
  const [tcpHttpRequestHeaders, setTcpHttpRequestHeaders] = useState<{key: string, value: string}[]>([]);
  const [tcpHttpResponseVersion, setTcpHttpResponseVersion] = useState('1.1');
  const [tcpHttpResponseStatus, setTcpHttpResponseStatus] = useState('200');
  const [tcpHttpResponseReason, setTcpHttpResponseReason] = useState('OK');
  const [tcpHttpResponseHeaders, setTcpHttpResponseHeaders] = useState<{key: string, value: string}[]>([]);

  // XHTTP 设置
  const [xhttpPath, setXhttpPath] = useState('/');
  const [xhttpHost, setXhttpHost] = useState('');
  const [xhttpMode, setXhttpMode] = useState('auto');
  const [xhttpHeaders, setXhttpHeaders] = useState<{key: string, value: string}[]>([]);
  const [xhttpXPaddingBytes, setXhttpXPaddingBytes] = useState('100-1000');
  const [xhttpNoGRPCHeader, setXhttpNoGRPCHeader] = useState(false);
  const [xhttpScMinPostsIntervalMs, setXhttpScMinPostsIntervalMs] = useState('30');
  const [xhttpXmuxMaxConcurrency, setXhttpXmuxMaxConcurrency] = useState('16-32');
  const [xhttpXmuxMaxConnections, setXhttpXmuxMaxConnections] = useState('0');
  const [xhttpXmuxCMaxReuseTimes, setXhttpXmuxCMaxReuseTimes] = useState('0');
  const [xhttpXmuxHMaxRequestTimes, setXhttpXmuxHMaxRequestTimes] = useState('600-900');
  const [xhttpXmuxHMaxReusableSecs, setXhttpXmuxHMaxReusableSecs] = useState('1800-3000');
  const [xhttpXmuxHKeepAlivePeriod, setXhttpXmuxHKeepAlivePeriod] = useState('0');
  const [xhttpDownloadAddress, setXhttpDownloadAddress] = useState('');
  const [xhttpDownloadPort, setXhttpDownloadPort] = useState('443');
  const [xhttpDownloadNetwork, setXhttpDownloadNetwork] = useState('xhttp');

  // mKCP 设置
  const [mkcpMtu, setMkcpMtu] = useState('1350');
  const [mkcpTti, setMkcpTti] = useState('20');
  const [mkcpUplinkCapacity, setMkcpUplinkCapacity] = useState('5');
  const [mkcpDownlinkCapacity, setMkcpDownlinkCapacity] = useState('20');
  const [mkcpCongestion, setMkcpCongestion] = useState(false);
  const [mkcpReadBufferSize, setMkcpReadBufferSize] = useState('1');
  const [mkcpWriteBufferSize, setMkcpWriteBufferSize] = useState('1');
  const [mkcpHeaderType, setMkcpHeaderType] = useState('none');
  const [mkcpHeaderDomain, setMkcpHeaderDomain] = useState('example.com');
  const [mkcpSeed, setMkcpSeed] = useState('Password');

  // gRPC 设置
  const [grpcAuthority, setGrpcAuthority] = useState('grpc.example.com');
  const [grpcServiceName, setGrpcServiceName] = useState('name');
  const [grpcUserAgent, setGrpcUserAgent] = useState('custom user agent');
  const [grpcMultiMode, setGrpcMultiMode] = useState(false);
  const [grpcIdleTimeout, setGrpcIdleTimeout] = useState('60');
  const [grpcHealthCheckTimeout, setGrpcHealthCheckTimeout] = useState('20');
  const [grpcPermitWithoutStream, setGrpcPermitWithoutStream] = useState(false);
  const [grpcInitialWindowsSize, setGrpcInitialWindowsSize] = useState('0');

  // WebSocket 设置
  const [wsPath, setWsPath] = useState('/');
  const [wsHost, setWsHost] = useState('xray.com');
  const [wsHeaders, setWsHeaders] = useState<{key: string, value: string}[]>([]);
  const [wsHeartbeatPeriod, setWsHeartbeatPeriod] = useState('10');

  // HTTP Upgrade 设置
  const [httpUpgradePath, setHttpUpgradePath] = useState('/');
  const [httpUpgradeHost, setHttpUpgradeHost] = useState('xray.com');
  const [httpUpgradeHeaders, setHttpUpgradeHeaders] = useState<{key: string, value: string}[]>([]);

  // 安全设置状态
  const [securityType, setSecurityType] = useState('none');
  // TLS 安全设置（出站）
  const [tlsServerName, setTlsServerName] = useState('');
  const [tlsAllowInsecure, setTlsAllowInsecure] = useState(false);
  const [tlsAlpn, setTlsAlpn] = useState<string[]>(['h2', 'http/1.1']);
  const [tlsFingerprint, setTlsFingerprint] = useState(''); // '', 'chrome', 'firefox', ...
  const [tlsPinnedPeerCerts, setTlsPinnedPeerCerts] = useState(''); // 用逗号分隔的base64 sha256 列表

  // Reality 安全设置（出站）
  const [realityFingerprint, setRealityFingerprint] = useState('');
  const [realityServerName, setRealityServerName] = useState('');
  const [realityPassword, setRealityPassword] = useState('');
  const [realityShortId, setRealityShortId] = useState('');
  const [realityTarget, setRealityTarget] = useState('');

  const [realitySpiderX, setRealitySpiderX] = useState('');

  // SockOpt 设置状态
  const [enableSockOpt, setEnableSockOpt] = useState(false);
  const [sockOptMark, setSockOptMark] = useState('0');
  const [sockOptTcpMaxSeg, setSockOptTcpMaxSeg] = useState('1440');
  const [sockOptTcpFastOpen, setSockOptTcpFastOpen] = useState(false);
  const [sockOptTproxy, setSockOptTproxy] = useState('off');
  const [sockOptDomainStrategy, setSockOptDomainStrategy] = useState('AsIs');
  const [sockOptDialerProxy, setSockOptDialerProxy] = useState('');
  const [sockOptTcpKeepAliveInterval, setSockOptTcpKeepAliveInterval] = useState('0');
  const [sockOptTcpKeepAliveIdle, setSockOptTcpKeepAliveIdle] = useState('300');
  const [sockOptTcpUserTimeout, setSockOptTcpUserTimeout] = useState('10000');
  const [sockOptTcpCongestion, setSockOptTcpCongestion] = useState('bbr');
  const [sockOptInterface, setSockOptInterface] = useState('');
  const [sockOptV6Only, setSockOptV6Only] = useState(false);
  const [sockOptTcpWindowClamp, setSockOptTcpWindowClamp] = useState('600');
  const [sockOptAddressPortStrategy, setSockOptAddressPortStrategy] = useState('none');

  // Bottom Sheet refs
  const muxBottomSheetRef = useRef<BottomSheetModal>(null);
  const fragmentBottomSheetRef = useRef<BottomSheetModal>(null);
  const transportBottomSheetRef = useRef<BottomSheetModal>(null);
  const securityBottomSheetRef = useRef<BottomSheetModal>(null);
  const sockOptBottomSheetRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['50%', '80%'], []);

  const isEditMode = index !== undefined;

  // Backdrop component for bottom sheets
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.3}
        pressBehavior="close"
      />
    ),
    []
  );

  // 获取当前配置
  const currentConfig = configs.find(c => c.id === configId) as ThreeXUIConfig;

  // 从serverConfig获取出站列表
  const serverConfig = getServerConfig(configId || '');
  const outbounds = serverConfig?.xray?.outbounds || [];
  const originalOutbound = isEditMode ? outbounds[parseInt(index!)] : null;

  // 协议链接导入功能
  const handleProtocolLinkImport = async () => {
    try {
      const clipboardText = await ClipboardAPI.getStringAsync();
      if (!clipboardText.trim()) {
        Alert.alert('错误', '剪切板为空');
        return;
      }

      // 检查是否为协议链接
      const supportedProtocols = ['vless://', 'vmess://', 'trojan://', 'ss://', 'socks5://', 'socks://', 'http://', 'https://', 'wireguard://'];
      const isProtocolLink = supportedProtocols.some(protocol => clipboardText.toLowerCase().startsWith(protocol));

      if (!isProtocolLink) {
        Alert.alert('错误', '剪切板内容不是支持的协议链接');
        return;
      }

      // 解析协议链接
      const converter = new XUIConfigConverter('');
      const outboundConfig = converter.parseProtocolLinkToOutbound(clipboardText);

      // 使用现有的loadEditModeConfig函数导入配置
      loadEditModeConfig(outboundConfig);
      Alert.alert('成功', '协议链接已导入到配置表单');
    } catch (error) {
      Alert.alert('错误', `导入失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 设置header右侧按钮（只在非编辑模式下显示）
  useEffect(() => {
    if (!isEditMode) {
      navigation.setOptions({
        headerRight: () => (
          <TouchableOpacity onPress={handleProtocolLinkImport} style={{ marginRight: 4 }}>
            <Link size={20} color={textColor} />
          </TouchableOpacity>
        ),
      });
    } else {
      navigation.setOptions({
        headerRight: undefined,
      });
    }
  }, [navigation, textColor, isEditMode]);

  // 加载编辑模式的配置
  useEffect(() => {
    if (isEditMode && originalOutbound) {
      loadEditModeConfig(originalOutbound);
    }
  }, [isEditMode, originalOutbound]);

  // 解析link参数
  useEffect(() => {
    if (link && !isEditMode) {
      try {
        const url = new URL(decodeURI(link));
        const params = new URLSearchParams(url.search);

        // 设置协议为wireguard
        setProtocol(OutboundProtocols.WIREGUARD);

        // 从链接的hash部分获取tag
        if (url.hash) {
          const tagFromHash = decodeURIComponent(url.hash.substring(1));
          setTag(tagFromHash);
        }

        // 解析wireguard参数
        const privateKey = params.get('privatekey');
        const publicKey = params.get('publickey');
        const address = params.get('address');
        const mtu = params.get('mtu');

        if (privateKey) setWgSecretKey(privateKey);
        if (publicKey) setWgPublicKey(publicKey);
        if (address) setWgAddress(address);
        if (mtu) setWgMtu(mtu);

        // 设置端点（从URL的host:port部分）
        const endpoint = `${url.hostname}:${url.port || '2408'}`;
        setWgEndpoint(endpoint);

      } catch (error) {
        console.error('Failed to parse link:', error);
        Alert.alert('错误', '解析Wireguard链接失败');
      }
    }
  }, [link, isEditMode]);

  // 加载配置的函数
  const loadEditModeConfig = (outbound: OutboundConfig) => {
    setTag(outbound.tag || '');
    setProtocol(outbound.protocol);
    setSendThrough(outbound.sendThrough || '');

    // Mux 设置
    if (outbound.mux) {
      setMuxEnabled(outbound.mux.enabled);
      setMuxConcurrency(outbound.mux.concurrency.toString());
      setMuxXudpConcurrency(outbound.mux.xudpConcurrency?.toString() || '16');
      setMuxXudpProxyUDP443(outbound.mux.xudpProxyUDP443 || 'reject');
    }

    // 代理设置
    if (outbound.proxySettings) {
      setProxyTag(outbound.proxySettings.tag);
    }

    // 加载传输设置
    if (outbound.streamSettings) {
      loadStreamSettings(outbound.streamSettings);
    }

    // 根据协议加载特定设置
    const settings = outbound.settings;
    switch (outbound.protocol) {
      case OutboundProtocols.FREEDOM:
        setDomainStrategy(settings.domainStrategy || 'AsIs');
        setRedirect(settings.redirect || '');
        // Load fragment settings
        if (settings.fragment) {
          setFragmentEnabled(true);
          setFragmentPackets(settings.fragment.packets || 'tlshello');
          setFragmentLength(settings.fragment.length || '100-200');
          setFragmentInterval(settings.fragment.interval || '10-20');
        }
        // Load noises settings
        if (settings.noises && settings.noises.length > 0) {
          setNoisesEnabled(true);
          setNoises(settings.noises);
        }
        // Load proxyProtocol
        setProxyProtocol(settings.proxyProtocol?.toString() || '0');
        break;
      case OutboundProtocols.BLACKHOLE:
        setBlackholeResponseType(settings.response?.type || 'none');
        break;
      case OutboundProtocols.DNS:
        setDnsNetwork(settings.network || 'tcp');
        setDnsAddress(settings.address || '*******');
        setDnsPort(settings.port?.toString() || '53');
        setDnsNonIPQuery(settings.nonIPQuery || 'drop');
        break;
      case OutboundProtocols.HTTP:
        if (settings.servers && settings.servers[0]) {
          const server = settings.servers[0];
          setHttpServerAddress(server.address || '');
          setHttpServerPort(server.port?.toString() || '');
          if (server.users && server.users[0]) {
            setHttpUsername(server.users[0].user || '');
            setHttpPassword(server.users[0].pass || '');
          }
        }
        break;
      case OutboundProtocols.SOCKS:
        if (settings.servers && settings.servers[0]) {
          const server = settings.servers[0];
          setSocksServerAddress(server.address || '');
          setSocksServerPort(server.port?.toString() || '');
          if (server.users && server.users[0]) {
            setSocksUsername(server.users[0].user || '');
            setSocksPassword(server.users[0].pass || '');
          }
        }
        break;
      case OutboundProtocols.SHADOWSOCKS:
        if (settings.servers && settings.servers[0]) {
          const server = settings.servers[0];
          setSsServerAddress(server.address || '');
          setSsServerPort(server.port?.toString() || '');
          setSsMethod(server.method || 'aes-256-gcm');
          setSsPassword(server.password || '');
          setSsUot(server.uot || false);
          setSsUotVersion(server.UoTVersion?.toString() || '0');
        }
        break;
      case OutboundProtocols.VLESS:
        if (settings.vnext && settings.vnext[0]) {
          const server = settings.vnext[0];
          setVlessServerAddress(server.address || '');
          setVlessServerPort(server.port?.toString() || '');
          if (server.users && server.users[0]) {
            setVlessUserId(server.users[0].id || '');
            setVlessFlow(server.users[0].flow || '');
          }
        }
        break;
      case OutboundProtocols.VMESS:
        if (settings.vnext && settings.vnext[0]) {
          const server = settings.vnext[0];
          setVmessServerAddress(server.address || '');
          setVmessServerPort(server.port?.toString() || '');
          if (server.users && server.users[0]) {
            setVmessUserId(server.users[0].id || '');
            setVmessSecurity(server.users[0].security || 'auto');
          }
        }
        break;
      case OutboundProtocols.TROJAN:
        if (settings.servers && settings.servers[0]) {
          const server = settings.servers[0];
          setTrojanServerAddress(server.address || '');
          setTrojanServerPort(server.port?.toString() || '');
          setTrojanPassword(server.password || '');
        }
        break;
      case OutboundProtocols.WIREGUARD:
        setWgSecretKey(settings.secretKey || '');
        if (settings.peers && settings.peers[0]) {
          setWgEndpoint(settings.peers[0].endpoint || '');
          setWgPublicKey(settings.peers[0].publicKey || '');
        }
        setWgMtu(settings.mtu?.toString() || '1420');
        setWgAddress(Array.isArray(settings.address) ? settings.address.join(', ') : (settings.address || ''));
        setWgNoKernelTun(settings.noKernelTun || false);
        setWgReserved(Array.isArray(settings.reserved) ? settings.reserved.join(', ') : '');
        setWgWorkers(settings.workers?.toString() || '');
        setWgDomainStrategy(settings.domainStrategy || 'ForceIP');
        break;
    }
  };

  // 加载传输设置
  const loadStreamSettings = (streamSettings: any) => {
    if (streamSettings.network) {
      setTransportType(streamSettings.network === 'ws' ? 'websocket' : streamSettings.network);
    }

    // TCP 设置
    if (streamSettings.tcpSettings) {
      const tcp = streamSettings.tcpSettings;
      if (tcp.header) {
        setTcpHeaderType(tcp.header.type || 'none');
        if (tcp.header.request) {
          setTcpHttpRequestVersion(tcp.header.request.version || '1.1');
          setTcpHttpRequestMethod(tcp.header.request.method || 'GET');
          setTcpHttpPaths(tcp.header.request.path || ['/']);
          if (tcp.header.request.headers) {
            const headers = Object.entries(tcp.header.request.headers).map(([key, value]) => ({
              key,
              value: Array.isArray(value) ? value[0] : value
            }));
            setTcpHttpRequestHeaders(headers);
          }
        }
        if (tcp.header.response) {
          setTcpHttpResponseVersion(tcp.header.response.version || '1.1');
          setTcpHttpResponseStatus(tcp.header.response.status || '200');
          setTcpHttpResponseReason(tcp.header.response.reason || 'OK');
          if (tcp.header.response.headers) {
            const headers = Object.entries(tcp.header.response.headers).map(([key, value]) => ({
              key,
              value: Array.isArray(value) ? value[0] : value
            }));
            setTcpHttpResponseHeaders(headers);
          }
        }
      }
    }

    // XHTTP 设置
    if (streamSettings.xhttpSettings) {
      const xhttp = streamSettings.xhttpSettings;
      setXhttpHost(xhttp.host || '');
      setXhttpPath(xhttp.path || '/');
      setXhttpMode(xhttp.mode || 'auto');
      if (xhttp.extra) {
        if (xhttp.extra.headers) {
          const headers = Object.entries(xhttp.extra.headers).map(([key, value]) => ({ key, value: String(value) }));
          setXhttpHeaders(headers);
        }
        setXhttpXPaddingBytes(xhttp.extra.xPaddingBytes || '100-1000');
        setXhttpNoGRPCHeader(xhttp.extra.noGRPCHeader || false);
        setXhttpScMinPostsIntervalMs(xhttp.extra.scMinPostsIntervalMs?.toString() || '30');
        if (xhttp.extra.xmux) {
          setXhttpXmuxMaxConcurrency(xhttp.extra.xmux.maxConcurrency || '16-32');
          setXhttpXmuxMaxConnections(xhttp.extra.xmux.maxConnections?.toString() || '0');
          setXhttpXmuxCMaxReuseTimes(xhttp.extra.xmux.cMaxReuseTimes?.toString() || '0');
          setXhttpXmuxHMaxRequestTimes(xhttp.extra.xmux.hMaxRequestTimes || '600-900');
          setXhttpXmuxHMaxReusableSecs(xhttp.extra.xmux.hMaxReusableSecs || '1800-3000');
          setXhttpXmuxHKeepAlivePeriod(xhttp.extra.xmux.hKeepAlivePeriod?.toString() || '0');
        }
        if (xhttp.extra.downloadSettings) {
          setXhttpDownloadAddress(xhttp.extra.downloadSettings.address || '');
          setXhttpDownloadPort(xhttp.extra.downloadSettings.port?.toString() || '443');
          setXhttpDownloadNetwork(xhttp.extra.downloadSettings.network || 'xhttp');
        }
      }
    }

    // mKCP 设置
    if (streamSettings.kcpSettings) {
      const kcp = streamSettings.kcpSettings;
      setMkcpMtu(kcp.mtu?.toString() || '1350');
      setMkcpTti(kcp.tti?.toString() || '20');
      setMkcpUplinkCapacity(kcp.uplinkCapacity?.toString() || '5');
      setMkcpDownlinkCapacity(kcp.downlinkCapacity?.toString() || '20');
      setMkcpCongestion(kcp.congestion || false);
      setMkcpReadBufferSize(kcp.readBufferSize?.toString() || '1');
      setMkcpWriteBufferSize(kcp.writeBufferSize?.toString() || '1');
      if (kcp.header) {
        setMkcpHeaderType(kcp.header.type || 'none');
        setMkcpHeaderDomain(kcp.header.domain || 'example.com');
      }
      setMkcpSeed(kcp.seed || 'Password');
    }

    // gRPC 设置
    if (streamSettings.grpcSettings) {
      const grpc = streamSettings.grpcSettings;
      setGrpcAuthority(grpc.authority || 'grpc.example.com');
      setGrpcServiceName(grpc.serviceName || 'name');
      setGrpcUserAgent(grpc.user_agent || 'custom user agent');
      setGrpcMultiMode(grpc.multiMode || false);
      setGrpcIdleTimeout(grpc.idle_timeout?.toString() || '60');
      setGrpcHealthCheckTimeout(grpc.health_check_timeout?.toString() || '20');
      setGrpcPermitWithoutStream(grpc.permit_without_stream || false);
      setGrpcInitialWindowsSize(grpc.initial_windows_size?.toString() || '0');
    }

    // WebSocket 设置
    if (streamSettings.wsSettings) {
      const ws = streamSettings.wsSettings;
      setWsPath(ws.path || '/');
      setWsHost(ws.host || 'xray.com');
      setWsHeartbeatPeriod(ws.heartbeatPeriod?.toString() || '10');
      if (ws.headers) {
        const headers = Object.entries(ws.headers).map(([key, value]) => ({ key, value: String(value) }));
        setWsHeaders(headers);
      }
    }

    // HTTP Upgrade 设置
    if (streamSettings.httpupgradeSettings) {
      const httpUpgrade = streamSettings.httpupgradeSettings;
      setHttpUpgradePath(httpUpgrade.path || '/');
      setHttpUpgradeHost(httpUpgrade.host || 'xray.com');
      if (httpUpgrade.headers) {
        const headers = Object.entries(httpUpgrade.headers).map(([key, value]) => ({ key, value: String(value) }));
        setHttpUpgradeHeaders(headers);
      }
    }

    // 安全设置
    if (streamSettings.security) {
      setSecurityType(streamSettings.security);

      // TLS 设置（出站）
      if (streamSettings.security === 'tls' && streamSettings.tlsSettings) {
        const tls = streamSettings.tlsSettings;
        setTlsServerName(tls.serverName || '');
        setTlsAllowInsecure(!!tls.allowInsecure);
        setTlsAlpn(Array.isArray(tls.alpn) ? tls.alpn : ['h2', 'http/1.1']);
        setTlsFingerprint(tls.fingerprint || '');
        setTlsPinnedPeerCerts(Array.isArray(tls.pinnedPeerCertificateChainSha256) ? tls.pinnedPeerCertificateChainSha256.join(',') : (tls.pinnedPeerCertificateChainSha256 || ''));
      }

      // Reality 设置（出站）
      if (streamSettings.security === 'reality' && streamSettings.realitySettings) {
        const reality = streamSettings.realitySettings;
        setRealityFingerprint(reality.fingerprint || '');
        setRealityServerName(reality.serverName || '');
        setRealityPassword(reality.password || '');
        setRealityShortId(reality.shortId || '');
        setRealityTarget(reality.target || reality.dest || '');
        setRealitySpiderX(reality.spiderX || '');
      }
    }

    // SockOpt 设置
    if (streamSettings.sockopt) {
      setEnableSockOpt(true);
      const sockopt = streamSettings.sockopt;
      setSockOptMark(sockopt.mark?.toString() || '0');
      setSockOptTcpMaxSeg(sockopt.tcpMaxSeg?.toString() || '1440');
      setSockOptTcpFastOpen(sockopt.tcpFastOpen || false);
      setSockOptTproxy(sockopt.tproxy || 'off');
      setSockOptDomainStrategy(sockopt.domainStrategy || 'AsIs');
      setSockOptDialerProxy(sockopt.dialerProxy || '');
      setSockOptTcpKeepAliveInterval(sockopt.tcpKeepAliveInterval?.toString() || '0');
      setSockOptTcpKeepAliveIdle(sockopt.tcpKeepAliveIdle?.toString() || '300');
      setSockOptTcpUserTimeout(sockopt.tcpUserTimeout?.toString() || '10000');
      setSockOptTcpCongestion(sockopt.tcpCongestion || 'bbr');
      setSockOptInterface(sockopt.interface || '');
      setSockOptV6Only(sockopt.V6Only || false);
      setSockOptTcpWindowClamp(sockopt.tcpWindowClamp?.toString() || '600');
      setSockOptAddressPortStrategy(sockopt.addressPortStrategy || 'none');
    }
  };

  // 重置表单
  const resetForm = () => {
    setTag('');
    setProtocol(OutboundProtocols.FREEDOM);
    setSendThrough('');
    setMuxEnabled(false);
    setMuxConcurrency('8');
    setMuxXudpConcurrency('16');
    setMuxXudpProxyUDP443('reject');
    setProxyTag('');

    // 重置协议特定设置
    setDomainStrategy('AsIs');
    setRedirect('');
    setFragmentEnabled(false);
    setFragmentPackets('tlshello');
    setFragmentLength('100-200');
    setFragmentInterval('10-20');
    setNoisesEnabled(false);
    setNoises([]);
    setProxyProtocol('0');
    setBlackholeResponseType('none');
    setDnsNetwork('tcp');
    setDnsAddress('*******');
    setDnsPort('53');
    setDnsNonIPQuery('drop');
    setHttpServerAddress('');
    setHttpServerPort('');
    setHttpUsername('');
    setHttpPassword('');
    setSocksServerAddress('');
    setSocksServerPort('');
    setSocksUsername('');
    setSocksPassword('');
    setSsServerAddress('');
    setSsServerPort('');
    setSsMethod('aes-256-gcm');
    setSsPassword('');
    setSsUot(false);
    setSsUotVersion('0');
    setVlessServerAddress('');
    setVlessServerPort('');
    setVlessUserId('');
    setVlessFlow('');
    setVmessServerAddress('');
    setVmessServerPort('');
    setVmessUserId('');
    setVmessSecurity('auto');
    setTrojanServerAddress('');
    setTrojanServerPort('');
    setTrojanPassword('');
    setWgSecretKey('');
    setWgEndpoint('');
    setWgPublicKey('');
    setWgMtu('1420');
    setWgAddress('');
    setWgNoKernelTun(false);
    setWgReserved('');
    setWgWorkers('');
    setWgDomainStrategy('ForceIP');

    // 重置传输设置
    setTransportType('tcp');
    setTcpHeaderType('none');
    setTcpHttpRequestVersion('1.1');
    setTcpHttpRequestMethod('GET');
    setTcpHttpPaths(['/']);
    setTcpHttpRequestHeaders([]);
    setTcpHttpResponseVersion('1.1');
    setTcpHttpResponseStatus('200');
    setTcpHttpResponseReason('OK');
    setTcpHttpResponseHeaders([]);
    setXhttpPath('/');
    setXhttpHost('');
    setXhttpMode('auto');
    setXhttpHeaders([]);
    setXhttpXPaddingBytes('100-1000');
    setXhttpNoGRPCHeader(false);
    setXhttpScMinPostsIntervalMs('30');
    setXhttpXmuxMaxConcurrency('16-32');
    setXhttpXmuxMaxConnections('0');
    setXhttpXmuxCMaxReuseTimes('0');
    setXhttpXmuxHMaxRequestTimes('600-900');
    setXhttpXmuxHMaxReusableSecs('1800-3000');
    setXhttpXmuxHKeepAlivePeriod('0');
    setXhttpDownloadAddress('');
    setXhttpDownloadPort('443');
    setXhttpDownloadNetwork('xhttp');
    setMkcpMtu('1350');
    setMkcpTti('20');
    setMkcpUplinkCapacity('5');
    setMkcpDownlinkCapacity('20');
    setMkcpCongestion(false);
    setMkcpReadBufferSize('1');
    setMkcpWriteBufferSize('1');
    setMkcpHeaderType('none');
    setMkcpHeaderDomain('example.com');
    setMkcpSeed('Password');
    setGrpcAuthority('grpc.example.com');
    setGrpcServiceName('name');
    setGrpcUserAgent('custom user agent');
    setGrpcMultiMode(false);
    setGrpcIdleTimeout('60');
    setGrpcHealthCheckTimeout('20');
    setGrpcPermitWithoutStream(false);
    setGrpcInitialWindowsSize('0');
    setWsPath('/');
    setWsHost('xray.com');
    setWsHeaders([]);
    setWsHeartbeatPeriod('10');
    setHttpUpgradePath('/');
    setHttpUpgradeHost('xray.com');
    setHttpUpgradeHeaders([]);

    // 重置安全设置
    setSecurityType('none');
    setTlsServerName('');
    setTlsAllowInsecure(false);
    setTlsAlpn(['h2', 'http/1.1']);
    setTlsFingerprint('');
    setTlsPinnedPeerCerts('');

    setRealityFingerprint('');
    setRealityServerName('');
    setRealityPassword('');
    setRealityShortId('');
    setRealityTarget('');
    setRealitySpiderX('');

    // 重置SockOpt设置
    setEnableSockOpt(false);
    setSockOptMark('0');
    setSockOptTcpMaxSeg('1440');
    setSockOptTcpFastOpen(false);
    setSockOptTproxy('off');
    setSockOptDomainStrategy('AsIs');
    setSockOptDialerProxy('');
    setSockOptTcpKeepAliveInterval('0');
    setSockOptTcpKeepAliveIdle('300');
    setSockOptTcpUserTimeout('10000');
    setSockOptTcpCongestion('bbr');
    setSockOptInterface('');
    setSockOptV6Only(false);
    setSockOptTcpWindowClamp('600');
    setSockOptAddressPortStrategy('none');
  };

  // 生成协议设置
  const generateProtocolSettings = () => {
    switch (protocol) {
      case OutboundProtocols.FREEDOM:
        const freedomSettings: any = {
          domainStrategy,
          redirect: redirect || undefined,
          proxyProtocol: parseInt(proxyProtocol) || 0,
        };

        // Add fragment settings if enabled
        if (fragmentEnabled) {
          freedomSettings.fragment = {
            packets: fragmentPackets,
            length: fragmentLength,
            interval: fragmentInterval
          };
        }

        // Add noises settings if enabled
        if (noisesEnabled && noises.length > 0) {
          freedomSettings.noises = noises;
        }

        return freedomSettings;
      case OutboundProtocols.BLACKHOLE:
        return {
          response: {
            type: blackholeResponseType,
          },
        };
      case OutboundProtocols.DNS:
        return {
          network: dnsNetwork,
          address: dnsAddress,
          port: parseInt(dnsPort) || 53,
          nonIPQuery: dnsNonIPQuery,
        };
      case OutboundProtocols.HTTP:
        const httpServer: any = {
          address: httpServerAddress,
          port: parseInt(httpServerPort) || 3128,
        };
        if (httpUsername && httpPassword) {
          httpServer.users = [{ user: httpUsername, pass: httpPassword }];
        }
        return { servers: [httpServer] };
      case OutboundProtocols.SOCKS:
        const socksServer: any = {
          address: socksServerAddress,
          port: parseInt(socksServerPort) || 1080,
        };
        if (socksUsername && socksPassword) {
          socksServer.users = [{ user: socksUsername, pass: socksPassword }];
        }
        return { servers: [socksServer] };
      case OutboundProtocols.SHADOWSOCKS:
        return {
          servers: [{
            address: ssServerAddress,
            port: parseInt(ssServerPort) || 8388,
            method: ssMethod,
            password: ssPassword,
            uot: ssUot,
            UoTVersion: parseInt(ssUotVersion) || 0,
          }],
        };
      case OutboundProtocols.VLESS:
        return {
          vnext: [{
            address: vlessServerAddress,
            port: parseInt(vlessServerPort) || 443,
            users: [{
              id: vlessUserId,
              encryption: 'none',
              flow: vlessFlow || undefined,
            }],
          }],
        };
      case OutboundProtocols.VMESS:
        return {
          vnext: [{
            address: vmessServerAddress,
            port: parseInt(vmessServerPort) || 443,
            users: [{
              id: vmessUserId,
              security: vmessSecurity,
            }],
          }],
        };
      case OutboundProtocols.TROJAN:
        return {
          servers: [{
            address: trojanServerAddress,
            port: parseInt(trojanServerPort) || 443,
            password: trojanPassword,
          }],
        };
      case OutboundProtocols.WIREGUARD:
        const wgSettings: any = {
          secretKey: wgSecretKey,
          peers: [{
            endpoint: wgEndpoint,
            publicKey: wgPublicKey,
          }],
          mtu: parseInt(wgMtu) || 1420,
        };

        // Add address if provided
        if (wgAddress.trim()) {
          const addresses = wgAddress.split(',').map(addr => addr.trim()).filter(addr => addr);
          if (addresses.length > 0) {
            wgSettings.address = addresses;
          }
        }

        // Add noKernelTun
        wgSettings.noKernelTun = wgNoKernelTun;

        // Add reserved if provided
        if (wgReserved.trim()) {
          const reservedValues = wgReserved.split(',').map(val => {
            const num = parseInt(val.trim());
            return isNaN(num) ? 0 : num;
          }).filter(val => val >= 0 && val <= 255);
          if (reservedValues.length > 0) {
            wgSettings.reserved = reservedValues;
          }
        }

        // Add workers if provided
        if (wgWorkers.trim()) {
          const workers = parseInt(wgWorkers);
          if (!isNaN(workers) && workers > 0) {
            wgSettings.workers = workers;
          }
        }

        // Add domainStrategy
        wgSettings.domainStrategy = wgDomainStrategy;

        return wgSettings;
      default:
        return {};
    }
  };

  // 生成SockOpt设置
  const generateSockOptSettings = () => {
    if (!enableSockOpt) {
      return {};
    }

    const sockOpt: any = {};

    // Mark
    if (sockOptMark) {
      const mark = parseInt(sockOptMark);
      if (!isNaN(mark)) {
        sockOpt.mark = mark;
      }
    }

    // TCP Max Seg
    if (sockOptTcpMaxSeg) {
      const tcpMaxSeg = parseInt(sockOptTcpMaxSeg);
      if (!isNaN(tcpMaxSeg)) {
        sockOpt.tcpMaxSeg = tcpMaxSeg;
      }
    }

    // TCP Fast Open
    if (sockOptTcpFastOpen) {
      sockOpt.tcpFastOpen = true;
    }

    // TProxy
    if (sockOptTproxy && sockOptTproxy !== 'off') {
      sockOpt.tproxy = sockOptTproxy;
    }

    // Domain Strategy
    if (sockOptDomainStrategy !== 'AsIs') {
      sockOpt.domainStrategy = sockOptDomainStrategy;
    }

    // Dialer Proxy
    if (sockOptDialerProxy.trim()) {
      sockOpt.dialerProxy = sockOptDialerProxy.trim();
    }

    // TCP Keep Alive Interval
    if (sockOptTcpKeepAliveInterval) {
      const interval = parseInt(sockOptTcpKeepAliveInterval);
      if (!isNaN(interval)) {
        sockOpt.tcpKeepAliveInterval = interval;
      }
    }

    // TCP Keep Alive Idle
    if (sockOptTcpKeepAliveIdle) {
      const idle = parseInt(sockOptTcpKeepAliveIdle);
      if (!isNaN(idle)) {
        sockOpt.tcpKeepAliveIdle = idle;
      }
    }

    // TCP User Timeout
    if (sockOptTcpUserTimeout) {
      const timeout = parseInt(sockOptTcpUserTimeout);
      if (!isNaN(timeout)) {
        sockOpt.tcpUserTimeout = timeout;
      }
    }

    // TCP Congestion
    if (sockOptTcpCongestion && sockOptTcpCongestion !== 'bbr') {
      sockOpt.tcpCongestion = sockOptTcpCongestion;
    }

    // Interface
    if (sockOptInterface.trim()) {
      sockOpt.interface = sockOptInterface.trim();
    }

    // V6 Only
    if (sockOptV6Only) {
      sockOpt.V6Only = true;
    }

    // TCP Window Clamp
    if (sockOptTcpWindowClamp) {
      const clamp = parseInt(sockOptTcpWindowClamp);
      if (!isNaN(clamp)) {
        sockOpt.tcpWindowClamp = clamp;
      }
    }

    // Address Port Strategy
    if (sockOptAddressPortStrategy && sockOptAddressPortStrategy !== 'none') {
      sockOpt.addressPortStrategy = sockOptAddressPortStrategy;
    }

    return Object.keys(sockOpt).length > 0 ? { sockopt: sockOpt } : {};
  };

  // 渲染协议特定设置
  const renderProtocolSettings = () => {
    switch (protocol) {
      case OutboundProtocols.FREEDOM:
        return (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>Freedom 设置</Text>

            <View style={styles.row}>
              <View style={[styles.inputGroup, styles.flex1]}>
                <Label style={[styles.label, { color: textColor }]}>域名策略</Label>
                <Select value={{ value: domainStrategy, label: domainStrategy }} onValueChange={(option) => setDomainStrategy(option?.value || 'AsIs')}>
                  <SelectTrigger style={[styles.input, { borderColor }]}>
                    <SelectValue placeholder="选择域名策略" className="text-foreground text-sm native:text-lg" />
                  </SelectTrigger>
                  <SelectContent>
                    <ScrollView style={{ maxHeight: 200 }}>
                      <SelectItem value="AsIs" label="AsIs">AsIs</SelectItem>
                      <SelectItem value="UseIP" label="UseIP">UseIP</SelectItem>
                      <SelectItem value="UseIPv6v4" label="UseIPv6v4">UseIPv6v4</SelectItem>
                      <SelectItem value="UseIPv6" label="UseIPv6">UseIPv6</SelectItem>
                      <SelectItem value="UseIPv4v6" label="UseIPv4v6">UseIPv4v6</SelectItem>
                      <SelectItem value="UseIPv4" label="UseIPv4">UseIPv4</SelectItem>
                      <SelectItem value="ForceIP" label="ForceIP">ForceIP</SelectItem>
                      <SelectItem value="ForceIPv6v4" label="ForceIPv6v4">ForceIPv6v4</SelectItem>
                      <SelectItem value="ForceIPv6" label="ForceIPv6">ForceIPv6</SelectItem>
                      <SelectItem value="ForceIPv4v6" label="ForceIPv4v6">ForceIPv4v6</SelectItem>
                      <SelectItem value="ForceIPv4" label="ForceIPv4">ForceIPv4</SelectItem>
                    </ScrollView>
                  </SelectContent>
                </Select>
              </View>
              <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                <Label style={[styles.label, { color: textColor }]}>代理协议版本</Label>
                <Select value={{ value: proxyProtocol, label: `版本 ${proxyProtocol}` }} onValueChange={(option) => setProxyProtocol(option?.value || '0')}>
                  <SelectTrigger style={[styles.input, { borderColor }]}>
                    <SelectValue placeholder="选择版本" className="text-foreground text-sm native:text-lg" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0" label="版本 0">版本 0</SelectItem>
                    <SelectItem value="1" label="版本 1">版本 1</SelectItem>
                    <SelectItem value="2" label="版本 2">版本 2</SelectItem>
                  </SelectContent>
                </Select>
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Label style={[styles.label, { color: textColor }]}>重定向地址 (可选)</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={redirect}
                onChangeText={setRedirect}
                placeholder="127.0.0.1:3366"
              />
            </View>

            {/* Fragment 设置 */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionTitle, { color: textColor }]}>Fragment 设置</Text>
                <View style={styles.sockOptControls}>
                  <Switch
                    checked={fragmentEnabled}
                    onCheckedChange={setFragmentEnabled}
                  />
                  <Button
                    onPress={() => fragmentBottomSheetRef.current?.present()}
                    size="sm"
                    variant="secondary"
                    disabled={!fragmentEnabled}
                    style={[!fragmentEnabled && styles.disabledButton]}
                  >
                    <Text style={[styles.editButtonText, { color: fragmentEnabled ? textColor : textColor + '50' }]}>编辑</Text>
                  </Button>
                </View>
              </View>

              {fragmentEnabled ? (
                <View style={[styles.transportInfo, { borderColor }]}>
                  <Text style={[styles.transportType, { color: textColor }]}>
                    Fragment: 已启用
                  </Text>
                  <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                    数据包类型: {fragmentPackets}
                  </Text>
                  <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                    长度范围: {fragmentLength}
                  </Text>
                  <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                    间隔: {fragmentInterval}ms
                  </Text>
                </View>
              ) : (
                <Text style={[styles.placeholder, { color: textColor + '80' }]}>
                  Fragment 已禁用
                </Text>
              )}
            </View>

            {/* Noises 设置 */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionTitle, { color: textColor }]}>Noises 设置</Text>
                <Button onPress={handleAddNoise} size="sm" variant="secondary">
                  <Text style={[styles.editButtonText, { color: textColor }]}>添加 Noise</Text>
                </Button>
              </View>

              {noises.length === 0 ? (
                <Text style={[styles.placeholder, { color: textColor + '80' }]}>
                  暂无 Noise 配置，点击上方按钮添加 Noise
                </Text>
              ) : (
                <View style={styles.userList}>
                  {noises.map((noise, index) => (
                    <View key={index} style={[styles.userCard, { borderColor }]}>
                      <View style={styles.userInfo}>
                        <Text style={[styles.userEmail, { color: textColor }]}>
                          Noise {index + 1}
                        </Text>
                        <Text style={[styles.userEmail, { color: textColor + '80', fontSize: 12 }]}>
                          类型: {noise.type} | 延迟: {noise.delay}ms
                        </Text>
                        <Text style={[styles.userEmail, { color: textColor + '80', fontSize: 12 }]}>
                          数据包: {noise.packet.substring(0, 20)}...
                        </Text>
                      </View>
                      <View style={styles.userActions}>
                        <TouchableOpacity onPress={() => handleEditNoise(noise, index)} style={styles.actionButton}>
                          <Edit size={16} color={textColor} />
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => handleDeleteNoise(index)} style={styles.actionButton}>
                          <Trash2 size={16} color="#ef4444" />
                        </TouchableOpacity>
                      </View>
                    </View>
                  ))}
                </View>
              )}
            </View>
          </View>
        );

      case OutboundProtocols.BLACKHOLE:
        const blackholeOptions: SelectionOption[] = [
          { id: 'none', label: '无响应' },
          { id: 'http', label: 'HTTP 响应' },
        ];

        return (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>Blackhole 设置</Text>

            <SelectionGroup
              label="响应类型"
              options={blackholeOptions}
              value={blackholeResponseType}
              onChange={(value) => setBlackholeResponseType(Array.isArray(value) ? value[0] : value)}
              multiple={false}
              horizontal={true}
              buttonSize="sm"
            />
          </View>
        );

      case OutboundProtocols.DNS:
        return (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>DNS 设置</Text>

            <View style={styles.row}>
              <View style={[styles.inputGroup, styles.flex1]}>
                <Label style={[styles.label, { color: textColor }]}>网络类型</Label>
                <Select value={{ value: dnsNetwork, label: dnsNetwork.toUpperCase() }} onValueChange={(option) => setDnsNetwork(option?.value || 'tcp')}>
                  <SelectTrigger style={[styles.input, { borderColor }]}>
                    <SelectValue placeholder="选择网络类型" className="text-foreground text-sm native:text-lg" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="tcp" label="TCP">TCP</SelectItem>
                    <SelectItem value="udp" label="UDP">UDP</SelectItem>
                  </SelectContent>
                </Select>
              </View>
              <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                <Label style={[styles.label, { color: textColor }]}>非IP查询处理</Label>
                <Select value={{ value: dnsNonIPQuery, label: dnsNonIPQuery === 'drop' ? '丢弃' : '跳过' }} onValueChange={(option) => setDnsNonIPQuery(option?.value || 'drop')}>
                  <SelectTrigger style={[styles.input, { borderColor }]}>
                    <SelectValue placeholder="选择处理方式" className="text-foreground text-sm native:text-lg" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="drop" label="丢弃">丢弃</SelectItem>
                    <SelectItem value="skip" label="跳过">跳过</SelectItem>
                  </SelectContent>
                </Select>
              </View>
            </View>

            <View style={styles.row}>
              <View style={[styles.inputGroup, styles.flex1]}>
                <Label style={[styles.label, { color: textColor }]}>DNS 服务器地址</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={dnsAddress}
                  onChangeText={setDnsAddress}
                  placeholder="*******"
                />
              </View>
              <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                <Label style={[styles.label, { color: textColor }]}>端口</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={dnsPort}
                  onChangeText={setDnsPort}
                  placeholder="53"
                  keyboardType="numeric"
                />
              </View>
            </View>
          </View>
        );

      case OutboundProtocols.HTTP:
        return (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>HTTP 代理设置</Text>

            <View style={styles.row}>
              <View style={[styles.inputGroup, styles.flex1]}>
                <Label style={[styles.label, { color: textColor }]}>服务器地址</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={httpServerAddress}
                  onChangeText={setHttpServerAddress}
                  placeholder="***********"
                />
              </View>
              <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                <Label style={[styles.label, { color: textColor }]}>端口</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={httpServerPort}
                  onChangeText={setHttpServerPort}
                  placeholder="3128"
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View style={styles.row}>
              <View style={[styles.flex1]}>
                <Label style={[styles.label, { color: textColor }]}>用户名 (可选)</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={httpUsername}
                  onChangeText={setHttpUsername}
                  placeholder="用户名"
                />
              </View>
              <View style={[styles.flex1, styles.marginLeft]}>
                <Label style={[styles.label, { color: textColor }]}>密码 (可选)</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={httpPassword}
                  onChangeText={setHttpPassword}
                  placeholder="密码"
                  secureTextEntry
                />
              </View>
            </View>
          </View>
        );

      case OutboundProtocols.SOCKS:
        return (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>SOCKS 代理设置</Text>

            <View style={styles.row}>
              <View style={[styles.inputGroup, styles.flex1]}>
                <Label style={[styles.label, { color: textColor }]}>服务器地址</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={socksServerAddress}
                  onChangeText={setSocksServerAddress}
                  placeholder="127.0.0.1"
                />
              </View>
              <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                <Label style={[styles.label, { color: textColor }]}>端口</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={socksServerPort}
                  onChangeText={setSocksServerPort}
                  placeholder="1080"
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View style={styles.row}>
              <View style={[styles.flex1]}>
                <Label style={[styles.label, { color: textColor }]}>用户名 (可选)</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={socksUsername}
                  onChangeText={setSocksUsername}
                  placeholder="用户名"
                />
              </View>
              <View style={[styles.flex1, styles.marginLeft]}>
                <Label style={[styles.label, { color: textColor }]}>密码 (可选)</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={socksPassword}
                  onChangeText={setSocksPassword}
                  placeholder="密码"
                  secureTextEntry
                />
              </View>
            </View>
          </View>
        );

      case OutboundProtocols.SHADOWSOCKS:
        return (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>Shadowsocks 设置</Text>

            <View style={styles.row}>
              <View style={[styles.inputGroup, styles.flex1]}>
                <Label style={[styles.label, { color: textColor }]}>服务器地址</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={ssServerAddress}
                  onChangeText={setSsServerAddress}
                  placeholder="example.com"
                />
              </View>
              <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                <Label style={[styles.label, { color: textColor }]}>端口</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={ssServerPort}
                  onChangeText={setSsServerPort}
                  placeholder="8388"
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View style={styles.section}>
              <Label style={[styles.label, { color: textColor }]}>加密方式</Label>
              <Select value={{ value: ssMethod, label: ssMethod }} onValueChange={(option) => setSsMethod(option?.value || 'aes-256-gcm')}>
                <SelectTrigger style={[styles.input, { borderColor }]}>
                  <SelectValue placeholder="选择加密方式" className="text-foreground text-sm native:text-lg" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="aes-256-gcm" label="aes-256-gcm">aes-256-gcm</SelectItem>
                  <SelectItem value="aes-128-gcm" label="aes-128-gcm">aes-128-gcm</SelectItem>
                  <SelectItem value="chacha20-poly1305" label="chacha20-poly1305">chacha20-poly1305</SelectItem>
                  <SelectItem value="xchacha20-poly1305" label="xchacha20-poly1305">xchacha20-poly1305</SelectItem>
                  <SelectItem value="2022-blake3-aes-128-gcm" label="2022-blake3-aes-128-gcm">2022-blake3-aes-128-gcm</SelectItem>
                  <SelectItem value="2022-blake3-aes-256-gcm" label="2022-blake3-aes-256-gcm">2022-blake3-aes-256-gcm</SelectItem>
                  <SelectItem value="2022-blake3-chacha20-poly1305" label="2022-blake3-chacha20-poly1305">2022-blake3-chacha20-poly1305</SelectItem>
                </SelectContent>
              </Select>
            </View>

            <View style={styles.section}>
              <Label style={[styles.label, { color: textColor }]}>密码</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={ssPassword}
                onChangeText={setSsPassword}
                placeholder="密码"
                secureTextEntry
              />
            </View>

            <View>
              <SelectionGroup
                label="UoT 设置"
                options={[
                  { id: 'disabled', label: '不启用' },
                  { id: '0', label: '0' },
                  { id: '1', label: '1' },
                ]}
                value={ssUot ? ssUotVersion : 'disabled'}
                onChange={(value) => {
                  const selectedValue = Array.isArray(value) ? value[0] : value;
                  if (selectedValue === 'disabled') {
                    setSsUot(false);
                    setSsUotVersion('0');
                  } else {
                    setSsUot(true);
                    setSsUotVersion(selectedValue);
                  }
                }}
                multiple={false}
                horizontal={true}
                buttonSize="sm"
              />
            </View>
          </View>
        );

      case OutboundProtocols.VLESS:
        return (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>VLESS 设置</Text>

            <View style={styles.row}>
              <View style={[styles.inputGroup, styles.flex1]}>
                <Label style={[styles.label, { color: textColor }]}>服务器地址</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={vlessServerAddress}
                  onChangeText={setVlessServerAddress}
                  placeholder="example.com"
                />
              </View>
              <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                <Label style={[styles.label, { color: textColor }]}>端口</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={vlessServerPort}
                  onChangeText={setVlessServerPort}
                  placeholder="443"
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Label style={[styles.label, { color: textColor }]}>用户 ID</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={vlessUserId}
                onChangeText={setVlessUserId}
                placeholder="5783a3e7-e373-51cd-8642-c83782b807c5"
              />
            </View>

            <View>
              <SelectionGroup
                label="流控 (可选)"
                options={[
                  { id: '', label: '无' },
                  { id: 'xtls-rprx-vision', label: 'xtls-rprx-vision' },
                  { id: 'xtls-rprx-vision-udp443', label: 'xtls-rprx-vision-udp443' },
                ]}
                value={vlessFlow}
                onChange={(value) => setVlessFlow(Array.isArray(value) ? value[0] : value)}
                multiple={false}
                horizontal={true}
                buttonSize="sm"
              />
            </View>
          </View>
        );

      case OutboundProtocols.VMESS:
        return (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>VMess 设置</Text>

            <View style={styles.row}>
              <View style={[styles.inputGroup, styles.flex1]}>
                <Label style={[styles.label, { color: textColor }]}>服务器地址</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={vmessServerAddress}
                  onChangeText={setVmessServerAddress}
                  placeholder="example.com"
                />
              </View>
              <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                <Label style={[styles.label, { color: textColor }]}>端口</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={vmessServerPort}
                  onChangeText={setVmessServerPort}
                  placeholder="443"
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View style={styles.row}>
              <View style={[styles.inputGroup, styles.flex1]}>
                <Label style={[styles.label, { color: textColor }]}>用户 ID</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={vmessUserId}
                  onChangeText={setVmessUserId}
                  placeholder="5783a3e7-e373-51cd-8642-c83782b807c5"
                />
              </View>
            </View>

            <View>
              <SelectionGroup
                label="加密方式"
                options={[
                  { id: 'auto', label: '自动' },
                  { id: 'aes-128-gcm', label: 'aes-128-gcm' },
                  { id: 'chacha20-poly1305', label: 'chacha20-poly1305' },
                  { id: 'none', label: 'none' },
                  { id: 'zero', label: 'zero' },
                ]}
                value={vmessSecurity}
                onChange={(value) => setVmessSecurity(Array.isArray(value) ? value[0] : value)}
                multiple={false}
                horizontal={true}
                buttonSize="sm"
              />
            </View>
          </View>
        );

      case OutboundProtocols.TROJAN:
        return (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>Trojan 设置</Text>

            <View style={styles.row}>
              <View style={[styles.inputGroup, styles.flex1]}>
                <Label style={[styles.label, { color: textColor }]}>服务器地址</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={trojanServerAddress}
                  onChangeText={setTrojanServerAddress}
                  placeholder="example.com"
                />
              </View>
              <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                <Label style={[styles.label, { color: textColor }]}>端口</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={trojanServerPort}
                  onChangeText={setTrojanServerPort}
                  placeholder="443"
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Label style={[styles.label, { color: textColor }]}>密码</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={trojanPassword}
                onChangeText={setTrojanPassword}
                placeholder="password"
                secureTextEntry
              />
            </View>
          </View>
        );

      case OutboundProtocols.WIREGUARD:
        return (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>Wireguard 设置</Text>

            <View style={styles.inputGroup}>
              <Label style={[styles.label, { color: textColor }]}>私钥</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={wgSecretKey}
                onChangeText={setWgSecretKey}
                placeholder="PRIVATE_KEY"
                secureTextEntry
              />
            </View>

            <View style={styles.row}>
              <View style={[styles.inputGroup, styles.flex1]}>
                <Label style={[styles.label, { color: textColor }]}>端点地址</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={wgEndpoint}
                  onChangeText={setWgEndpoint}
                  placeholder="example.com:51820"
                />
              </View>
              <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                <Label style={[styles.label, { color: textColor }]}>MTU</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={wgMtu}
                  onChangeText={setWgMtu}
                  placeholder="1420"
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Label style={[styles.label, { color: textColor }]}>对端公钥</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={wgPublicKey}
                onChangeText={setWgPublicKey}
                placeholder="PUBLIC_KEY"
              />
            </View>

            <View style={styles.inputGroup}>
              <Label style={[styles.label, { color: textColor }]}>地址 (可选)</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={wgAddress}
                onChangeText={setWgAddress}
                placeholder="********/32, fd00::2/128"
              />
            </View>

            <View style={styles.row}>
              <View style={[styles.inputGroup, styles.flex1]}>
                <Label style={[styles.label, { color: textColor }]}>Reserved (用逗号分隔)</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={wgReserved}
                  onChangeText={setWgReserved}
                  placeholder="1, 2, 3"
                />
              </View>
              <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                <Label style={[styles.label, { color: textColor }]}>Workers (可选)</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={wgWorkers}
                  onChangeText={setWgWorkers}
                  placeholder="默认为 CPU 核心数"
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View style={styles.row}>
              <View style={[styles.flex1]}>
                <Label style={[styles.label, { color: textColor }]}>域名策略</Label>
                <Select value={{ value: wgDomainStrategy, label: wgDomainStrategy }} onValueChange={(option) => setWgDomainStrategy(option?.value || 'ForceIP')}>
                  <SelectTrigger style={[styles.input, { borderColor }]}>
                    <SelectValue placeholder="选择域名策略" className="text-foreground text-sm native:text-lg" />
                  </SelectTrigger>
                  <SelectContent>
                    <ScrollView style={{ maxHeight: 200 }}>
                      <SelectItem value="ForceIPv6v4" label="ForceIPv6v4">ForceIPv6v4</SelectItem>
                      <SelectItem value="ForceIPv6" label="ForceIPv6">ForceIPv6</SelectItem>
                      <SelectItem value="ForceIPv4v6" label="ForceIPv4v6">ForceIPv4v6</SelectItem>
                      <SelectItem value="ForceIPv4" label="ForceIPv4">ForceIPv4</SelectItem>
                      <SelectItem value="ForceIP" label="ForceIP">ForceIP</SelectItem>
                    </ScrollView>
                  </SelectContent>
                </Select>
              </View>
              <View style={[styles.flex1, styles.marginLeft]}>
                <View style={[styles.switchRowCompact]}>
                  <Switch
                    checked={wgNoKernelTun}
                    onCheckedChange={setWgNoKernelTun}
                  />
                  <Label style={[styles.labelRight, { color: textColor }]}>No Kernel Tun</Label>
                </View>
              </View>
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  // 处理保存
  const handleSave = async () => {
    try {
      // 验证必填字段
      if (!tag.trim()) {
        Alert.alert('错误', '请输入标签');
        return;
      }

      // 验证协议特定字段
      if (!validateProtocolFields()) {
        return;
      }

      // 生成出站配置对象
      const newOutbound: OutboundConfig = {
        tag: tag.trim(),
        protocol,
        settings: generateProtocolSettings(),
        sendThrough: sendThrough || undefined,
      };

      // 添加传输设置（仅对支持的协议）
      if (protocol === OutboundProtocols.VLESS || protocol === OutboundProtocols.VMESS ||
          protocol === OutboundProtocols.SHADOWSOCKS || protocol === OutboundProtocols.TROJAN) {
        newOutbound.streamSettings = generateStreamSettings();
      }

      // 添加代理设置
      if (proxyTag.trim()) {
        newOutbound.proxySettings = { tag: proxyTag.trim() };
      }

      // 添加多路复用设置
      if (muxEnabled && (protocol === OutboundProtocols.VLESS || protocol === OutboundProtocols.VMESS ||
          protocol === OutboundProtocols.SHADOWSOCKS || protocol === OutboundProtocols.TROJAN ||
          protocol === OutboundProtocols.HTTP || protocol === OutboundProtocols.SOCKS)) {
        newOutbound.mux = {
          enabled: true,
          concurrency: parseInt(muxConcurrency) || 8,
          xudpConcurrency: parseInt(muxXudpConcurrency) || 16,
          xudpProxyUDP443: muxXudpProxyUDP443,
        };
      }

      // 更新serverConfig
      let serverConfig = getServerConfig(configId || '');
      if (!serverConfig) {
        serverConfig = {};
      }
      if (!serverConfig.xray) {
        serverConfig.xray = { outbounds: [] };
      }

      if (isEditMode && originalOutbound) {
        // 编辑模式：深合并以向后兼容（参考入站配置表单的做法）
        const mergedOutbound = merge({}, originalOutbound, newOutbound);
        serverConfig.xray.outbounds[parseInt(index!)] = mergedOutbound;
      } else {
        // 添加模式：添加新配置
        serverConfig.xray.outbounds.push(newOutbound);
      }

      // 同步到后端：POST /panel/xray/update，表单数据 xraySetting: xraySettingObj
      const config = configs.find(c => c.id === configId) as ThreeXUIConfig | undefined;
      if (!config) {
        Alert.alert('错误', '未找到服务器配置');
        return;
      }

      // 组装完整的 xraySetting（沿用 serverConfig 其他部分，仅更新 outbounds）
      const xraySettingObj = {
        ...(serverConfig.xray || {}),
        outbounds: serverConfig.xray?.outbounds || [],
      } as any;

      const formData = new FormData();
      formData.append('xraySetting', JSON.stringify(xraySettingObj));

      const response = await smartFetch(
        `${config.protocol}://${config.url}/panel/xray/update`,
        { method: 'POST', body: formData },
        config
      );

      const result = await response.json();
      if (result.success) {
        // 更新本地缓存的 xray 配置（从后端再拉一遍，保证一致）
        await getThreeXUIXrayConfig(config);
        Alert.alert('成功', isEditMode ? '出站配置已更新' : '出站配置已添加', [
          { text: '确定', onPress: () => router.back() }
        ]);
      } else {
        Alert.alert('错误', result.msg || '保存失败');
      }
    } catch (error) {
      console.error('Save outbound config failed:', error);
      Alert.alert('错误', '保存失败');
    }
  };

  // 验证协议特定字段
  const validateProtocolFields = () => {
    switch (protocol) {
      case OutboundProtocols.HTTP:
        if (!httpServerAddress.trim()) {
          Alert.alert('错误', '请输入HTTP服务器地址');
          return false;
        }
        break;
      case OutboundProtocols.SOCKS:
        if (!socksServerAddress.trim()) {
          Alert.alert('错误', '请输入SOCKS服务器地址');
          return false;
        }
        break;
      case OutboundProtocols.SHADOWSOCKS:
        if (!ssServerAddress.trim() || !ssPassword.trim()) {
          Alert.alert('错误', '请输入Shadowsocks服务器地址和密码');
          return false;
        }
        break;
      case OutboundProtocols.VLESS:
        if (!vlessServerAddress.trim() || !vlessUserId.trim()) {
          Alert.alert('错误', '请输入VLESS服务器地址和用户ID');
          return false;
        }
        break;
      case OutboundProtocols.VMESS:
        if (!vmessServerAddress.trim() || !vmessUserId.trim()) {
          Alert.alert('错误', '请输入VMess服务器地址和用户ID');
          return false;
        }
        break;
      case OutboundProtocols.TROJAN:
        if (!trojanServerAddress.trim() || !trojanPassword.trim()) {
          Alert.alert('错误', '请输入Trojan服务器地址和密码');
          return false;
        }
        break;
      case OutboundProtocols.WIREGUARD:
        if (!wgSecretKey.trim() || !wgEndpoint.trim() || !wgPublicKey.trim()) {
          Alert.alert('错误', '请输入Wireguard密钥、端点和公钥');
          return false;
        }
        break;
    }
    return true;
  };

  // 生成传输设置
  const generateStreamSettings = () => {
    let streamSettings: any = {
      network: transportType === 'websocket' ? 'ws' : transportType,
    };

    if (transportType === 'tcp') {
      streamSettings.tcpSettings = {
        header: tcpHeaderType === 'http' ? {
          type: 'http',
          request: {
            version: tcpHttpRequestVersion,
            method: tcpHttpRequestMethod,
            path: tcpHttpPaths,
            headers: tcpHttpRequestHeaders.reduce((acc, item) => {
              if (!acc[item.key]) {
                acc[item.key] = [];
              }
              acc[item.key].push(item.value);
              return acc;
            }, {} as Record<string, string[]>)
          },
          response: {
            version: tcpHttpResponseVersion,
            status: tcpHttpResponseStatus,
            reason: tcpHttpResponseReason,
            headers: tcpHttpResponseHeaders.reduce((acc, item) => {
              if (!acc[item.key]) {
                acc[item.key] = [];
              }
              acc[item.key].push(item.value);
              return acc;
            }, {} as Record<string, string[]>)
          }
        } : {
          type: 'none'
        }
      };
    } else if (transportType === 'xhttp') {
      // 构建headers对象
      const headers: { [key: string]: string } = {};
      xhttpHeaders.forEach(header => {
        if (header.key && header.value) {
          headers[header.key] = header.value;
        }
      });

      const xhttpSettings: any = {
        host: xhttpHost,
        path: xhttpPath,
        mode: xhttpMode,
        extra: {
          headers,
          xPaddingBytes: xhttpXPaddingBytes,
          noGRPCHeader: xhttpNoGRPCHeader,
          scMinPostsIntervalMs: parseInt(xhttpScMinPostsIntervalMs) || 30,
          xmux: {
            maxConcurrency: xhttpXmuxMaxConcurrency,
            maxConnections: parseInt(xhttpXmuxMaxConnections) || 0,
            cMaxReuseTimes: parseInt(xhttpXmuxCMaxReuseTimes) || 0,
            hMaxRequestTimes: xhttpXmuxHMaxRequestTimes,
            hMaxReusableSecs: xhttpXmuxHMaxReusableSecs,
            hKeepAlivePeriod: parseInt(xhttpXmuxHKeepAlivePeriod) || 0
          }
        }
      };

      // 添加下载设置（仅客户端）
      if (xhttpDownloadAddress) {
        xhttpSettings.extra.downloadSettings = {
          address: xhttpDownloadAddress,
          port: parseInt(xhttpDownloadPort) || 443,
          network: xhttpDownloadNetwork
        };
      }

      streamSettings.xhttpSettings = xhttpSettings;
    } else if (transportType === 'kcp') {
      streamSettings.kcpSettings = {
        mtu: parseInt(mkcpMtu) || 1350,
        tti: parseInt(mkcpTti) || 20,
        uplinkCapacity: parseInt(mkcpUplinkCapacity) || 5,
        downlinkCapacity: parseInt(mkcpDownlinkCapacity) || 20,
        congestion: mkcpCongestion,
        readBufferSize: parseInt(mkcpReadBufferSize) || 1,
        writeBufferSize: parseInt(mkcpWriteBufferSize) || 1,
        header: {
          type: mkcpHeaderType,
          domain: mkcpHeaderDomain
        },
        seed: mkcpSeed
      };
    } else if (transportType === 'grpc') {
      streamSettings.grpcSettings = {
        authority: grpcAuthority,
        serviceName: grpcServiceName,
        user_agent: grpcUserAgent,
        multiMode: grpcMultiMode,
        idle_timeout: parseInt(grpcIdleTimeout) || 60,
        health_check_timeout: parseInt(grpcHealthCheckTimeout) || 20,
        permit_without_stream: grpcPermitWithoutStream,
        initial_windows_size: parseInt(grpcInitialWindowsSize) || 0
      };
    } else if (transportType === 'websocket') {
      // 构建headers对象
      const headers: { [key: string]: string } = {};
      wsHeaders.forEach(header => {
        if (header.key && header.value) {
          headers[header.key] = header.value;
        }
      });

      streamSettings.wsSettings = {
        path: wsPath,
        host: wsHost,
        heartbeatPeriod: parseInt(wsHeartbeatPeriod) || 10,
        headers
      };
    } else if (transportType === 'httpupgrade') {
      // 构建headers对象
      const headers: { [key: string]: string } = {};
      httpUpgradeHeaders.forEach(header => {
        if (header.key && header.value) {
          headers[header.key] = header.value;
        }
      });

      streamSettings.httpupgradeSettings = {
        path: httpUpgradePath,
        host: httpUpgradeHost,
        headers
      };
    }

    // 添加安全设置
    if (securityType !== 'none') {
      streamSettings.security = securityType;
      if (securityType === 'tls') {
        streamSettings.tlsSettings = {
          serverName: tlsServerName,
          allowInsecure: tlsAllowInsecure,
          alpn: tlsAlpn,
          fingerprint: tlsFingerprint || undefined,
          pinnedPeerCertificateChainSha256: tlsPinnedPeerCerts
            ? tlsPinnedPeerCerts.split(/\r?\n/).map((x) => x.trim()).filter(Boolean)
            : []
        };
      } else if (securityType === 'reality') {
        streamSettings.realitySettings = {
          fingerprint: realityFingerprint || undefined,
          serverName: realityServerName,
          password: realityPassword,
          shortId: realityShortId,
          target: realityTarget,
          dest: realityTarget, // alias for target
          spiderX: realitySpiderX
        };
      }
    }

    // 合并SockOpt设置
    const sockOptSettings = generateSockOptSettings();
    streamSettings = { ...streamSettings, ...sockOptSettings };

    return streamSettings;
  };

  // TCP Header 管理函数
  const handleAddTcpRequestHeader = () => {
    setTcpHttpRequestHeaders([...tcpHttpRequestHeaders, { key: 'Host', value: '' }]);
  };

  const handleUpdateTcpRequestHeader = (index: number, field: 'key' | 'value', value: string) => {
    const newHeaders = [...tcpHttpRequestHeaders];
    newHeaders[index][field] = value;
    setTcpHttpRequestHeaders(newHeaders);
  };

  const handleRemoveTcpRequestHeader = (index: number) => {
    const newHeaders = [...tcpHttpRequestHeaders];
    newHeaders.splice(index, 1);
    setTcpHttpRequestHeaders(newHeaders);
  };

  const handleAddTcpResponseHeader = () => {
    setTcpHttpResponseHeaders([...tcpHttpResponseHeaders, { key: 'Content-Type', value: '' }]);
  };

  const handleUpdateTcpResponseHeader = (index: number, field: 'key' | 'value', value: string) => {
    const newHeaders = [...tcpHttpResponseHeaders];
    newHeaders[index][field] = value;
    setTcpHttpResponseHeaders(newHeaders);
  };

  const handleRemoveTcpResponseHeader = (index: number) => {
    const newHeaders = [...tcpHttpResponseHeaders];
    newHeaders.splice(index, 1);
    setTcpHttpResponseHeaders(newHeaders);
  };

  // XHTTP Header 管理函数
  const handleAddXhttpHeader = () => {
    setXhttpHeaders([...xhttpHeaders, { key: '', value: '' }]);
  };

  const handleUpdateXhttpHeader = (index: number, field: 'key' | 'value', value: string) => {
    const newHeaders = [...xhttpHeaders];
    newHeaders[index][field] = value;
    setXhttpHeaders(newHeaders);
  };

  const handleRemoveXhttpHeader = (index: number) => {
    const newHeaders = [...xhttpHeaders];
    newHeaders.splice(index, 1);
    setXhttpHeaders(newHeaders);
  };

  // WebSocket Header 管理函数
  const handleAddWsHeader = () => {
    setWsHeaders([...wsHeaders, { key: '', value: '' }]);
  };

  const handleUpdateWsHeader = (index: number, field: 'key' | 'value', value: string) => {
    const newHeaders = [...wsHeaders];
    newHeaders[index][field] = value;
    setWsHeaders(newHeaders);
  };

  const handleRemoveWsHeader = (index: number) => {
    const newHeaders = [...wsHeaders];
    newHeaders.splice(index, 1);
    setWsHeaders(newHeaders);
  };

  // HTTP Upgrade Header 管理函数
  const handleAddHttpUpgradeHeader = () => {
    setHttpUpgradeHeaders([...httpUpgradeHeaders, { key: '', value: '' }]);
  };

  const handleUpdateHttpUpgradeHeader = (index: number, field: 'key' | 'value', value: string) => {
    const newHeaders = [...httpUpgradeHeaders];
    newHeaders[index][field] = value;
    setHttpUpgradeHeaders(newHeaders);
  };

  const handleRemoveHttpUpgradeHeader = (index: number) => {
    const newHeaders = [...httpUpgradeHeaders];
    newHeaders.splice(index, 1);
    setHttpUpgradeHeaders(newHeaders);
  };

  // Noise 管理函数
  const handleAddNoise = () => {
    setEditingNoise(null);
    setNoiseType('base64');
    setNoisePacket('7nQBAAABAAAAAAAABnQtcmluZwZtc2VkZ2UDbmV0AAABAAE=');
    setNoiseDelay('10-16');
    setShowNoiseDialog(true);
  };

  const handleEditNoise = (noise: any, index: number) => {
    setEditingNoise({ ...noise, index });
    setNoiseType(noise.type);
    setNoisePacket(noise.packet);
    setNoiseDelay(noise.delay);
    setShowNoiseDialog(true);
  };

  const handleSaveNoise = () => {
    if (!noisePacket.trim()) {
      Alert.alert('错误', '请输入数据包内容');
      return;
    }

    const newNoise = {
      type: noiseType,
      packet: noisePacket.trim(),
      delay: noiseDelay.trim() || '10-16'
    };

    if (editingNoise) {
      // 编辑模式
      const newNoises = [...noises];
      newNoises[editingNoise.index] = newNoise;
      setNoises(newNoises);
    } else {
      // 添加模式
      setNoises([...noises, newNoise]);
    }

    setShowNoiseDialog(false);
    setEditingNoise(null);
  };

  const handleDeleteNoise = (index: number) => {
    Alert.alert(
      '确认删除',
      `确定要删除 Noise ${index + 1} 吗？`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: () => {
            const newNoises = noises.filter((_, i) => i !== index);
            setNoises(newNoises);
          }
        }
      ]
    );
  };

  return (
    <>
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        {/* 基本设置 */}
        <View style={styles.section}>
            <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>基本设置</Text>

            <View style={styles.row}>
              <View style={[styles.inputGroup, styles.flex1]}>
                <Label style={[styles.label, { color: textColor }]}>标签</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={tag}
                  onChangeText={setTag}
                  placeholder="出站标签"
                />
              </View>
              <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                <Label style={[styles.label, { color: textColor }]}>协议</Label>
                <Select value={{ value: protocol, label: protocol }} onValueChange={(option) => setProtocol(option?.value || OutboundProtocols.FREEDOM)}>
                  <SelectTrigger style={[styles.input, { borderColor }]}>
                    <SelectValue placeholder="选择协议" className="text-foreground text-sm native:text-lg" />
                  </SelectTrigger>
                  <SelectContent>
                    <ScrollView>
                    <SelectItem value={OutboundProtocols.FREEDOM} label="Freedom">Freedom</SelectItem>
                    <SelectItem value={OutboundProtocols.BLACKHOLE} label="Blackhole">Blackhole</SelectItem>
                    <SelectItem value={OutboundProtocols.DNS} label="DNS">DNS</SelectItem>
                    <SelectItem value={OutboundProtocols.HTTP} label="HTTP">HTTP</SelectItem>
                    <SelectItem value={OutboundProtocols.SOCKS} label="SOCKS">SOCKS</SelectItem>
                    <SelectItem value={OutboundProtocols.SHADOWSOCKS} label="Shadowsocks">Shadowsocks</SelectItem>
                    <SelectItem value={OutboundProtocols.TROJAN} label="Trojan">Trojan</SelectItem>
                    <SelectItem value={OutboundProtocols.VLESS} label="VLESS">VLESS</SelectItem>
                    <SelectItem value={OutboundProtocols.VMESS} label="VMess">VMess</SelectItem>
                    <SelectItem value={OutboundProtocols.WIREGUARD} label="Wireguard">Wireguard</SelectItem>
                    </ScrollView>
                  </SelectContent>
                </Select>
              </View>
            </View>

            <View style={styles.row}>
              <View style={styles.flex1}>
                <Label style={[styles.label, { color: textColor }]}>代理标签 (可选)</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={proxyTag}
                  onChangeText={setProxyTag}
                  placeholder="另一个出站标签"
                />
              </View>
              <View style={[styles.flex1, styles.marginLeft]}>
                <Label style={[styles.label, { color: textColor }]}>发送地址 (可选)</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={sendThrough}
                  onChangeText={setSendThrough}
                  placeholder="0.0.0.0"
                />
              </View>
            </View>
          </View>

          {/* 协议特定设置 */}
          {renderProtocolSettings()}

          {/* 传输设置 */}
          {(protocol === OutboundProtocols.VLESS || protocol === OutboundProtocols.VMESS ||
            protocol === OutboundProtocols.SHADOWSOCKS || protocol === OutboundProtocols.TROJAN) && (
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionTitle, { color: textColor }]}>传输设置</Text>
                <Button onPress={() => transportBottomSheetRef.current?.present()} size="sm" variant="secondary">
                  <Text style={[styles.editButtonText, { color: textColor }]}>编辑</Text>
                </Button>
              </View>

              <View style={[styles.transportInfo, { borderColor }]}>
                <Text style={[styles.transportType, { color: textColor }]}>
                  传输方式: {transportType.toUpperCase()}
                </Text>
                {transportType === 'tcp' && tcpHeaderType !== 'none' && (
                  <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                    伪装类型: {tcpHeaderType}
                  </Text>
                )}
                {transportType === 'xhttp' && (
                  <>
                    <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                      主机: {xhttpHost || '未设置'}, 路径: {xhttpPath}
                    </Text>
                    <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                      模式: {xhttpMode}, Headers: {xhttpHeaders.length}个
                    </Text>
                  </>
                )}
                {transportType === 'kcp' && (
                  <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                    MTU: {mkcpMtu}, TTI: {mkcpTti}, 伪装: {mkcpHeaderType}
                  </Text>
                )}
                {transportType === 'grpc' && (
                  <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                    服务名: {grpcServiceName}, multiMode: {grpcMultiMode ? '是' : '否'}
                  </Text>
                )}
                {transportType === 'websocket' && (
                  <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                    路径: {wsPath}, 主机: {wsHost}
                  </Text>
                )}
                {transportType === 'httpupgrade' && (
                  <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                    路径: {httpUpgradePath}, 主机: {httpUpgradeHost}
                  </Text>
                )}
              </View>
            </View>
          )}

          {/* 安全设置 */}
          {(protocol === OutboundProtocols.VLESS || protocol === OutboundProtocols.VMESS ||
            protocol === OutboundProtocols.SHADOWSOCKS || protocol === OutboundProtocols.TROJAN) && (
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionTitle, { color: textColor }]}>安全设置</Text>
                <Button onPress={() => securityBottomSheetRef.current?.present()} size="sm" variant="secondary">
                  <Text style={[styles.editButtonText, { color: textColor }]}>编辑</Text>
                </Button>
              </View>

              <View style={[styles.transportInfo, { borderColor }]}>
                <Text style={[styles.transportType, { color: textColor }]}>
                  安全类型: {securityType === 'none' ? '无' : securityType.toUpperCase()}
                </Text>
                <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                  配置详情请点击编辑按钮查看
                </Text>
              </View>
            </View>
          )}

          {/* SockOpt设置 */}
          {protocol && (
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionTitle, { color: textColor }]}>SockOpt设置</Text>
                <View style={styles.sockOptControls}>
                  <Switch
                    checked={enableSockOpt}
                    onCheckedChange={setEnableSockOpt}
                  />
                  <Button
                    onPress={() => sockOptBottomSheetRef.current?.present()}
                    size="sm"
                    variant="secondary"
                    disabled={!enableSockOpt}
                    style={[!enableSockOpt && styles.disabledButton]}
                  >
                    <Text style={[styles.editButtonText, { color: enableSockOpt ? textColor : textColor + '50' }]}>编辑</Text>
                  </Button>
                </View>
              </View>

              {enableSockOpt ? (
                <View style={[styles.transportInfo, { borderColor }]}>
                  <Text style={[styles.transportType, { color: textColor }]}>
                    SockOpt: 已启用
                  </Text>
                  {sockOptMark && (
                    <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                      Mark: {sockOptMark}
                    </Text>
                  )}
                  {sockOptTcpFastOpen && (
                    <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                      TCP Fast Open: 已启用
                    </Text>
                  )}
                  {sockOptTproxy && sockOptTproxy !== 'off' && (
                    <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                      TProxy: {sockOptTproxy}
                    </Text>
                  )}
                  {sockOptDomainStrategy !== 'AsIs' && (
                    <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                      域名策略: {sockOptDomainStrategy}
                    </Text>
                  )}
                </View>
              ) : (
                <Text style={[styles.placeholder, { color: textColor + '80' }]}>
                  SockOpt 已禁用
                </Text>
              )}
            </View>
          )}

          {/* 多路复用设置 */}
          {(protocol === OutboundProtocols.VLESS || protocol === OutboundProtocols.VMESS ||
            protocol === OutboundProtocols.SHADOWSOCKS || protocol === OutboundProtocols.TROJAN ||
            protocol === OutboundProtocols.HTTP || protocol === OutboundProtocols.SOCKS) && (
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionTitle, { color: textColor }]}>多路复用 (Mux)</Text>
                <View style={styles.sockOptControls}>
                  <Switch
                    checked={muxEnabled}
                    onCheckedChange={setMuxEnabled}
                  />
                  <Button
                    onPress={() => muxBottomSheetRef.current?.present()}
                    size="sm"
                    variant="secondary"
                    disabled={!muxEnabled}
                    style={[!muxEnabled && styles.disabledButton]}
                  >
                    <Text style={[styles.editButtonText, { color: muxEnabled ? textColor : textColor + '50' }]}>编辑</Text>
                  </Button>
                </View>
              </View>

              {muxEnabled ? (
                <View style={[styles.transportInfo, { borderColor }]}>
                  <Text style={[styles.transportType, { color: textColor }]}>
                    多路复用: 已启用
                  </Text>
                  <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                    并发数: {muxConcurrency}, XUDP并发数: {muxXudpConcurrency}
                  </Text>
                  <Text style={[styles.transportDetail, { color: textColor + '80' }]}>
                    UDP 443处理: {muxXudpProxyUDP443 === 'reject' ? '拒绝' : muxXudpProxyUDP443 === 'allow' ? '允许' : '跳过'}
                  </Text>
                </View>
              ) : (
                <Text style={[styles.placeholder, { color: textColor + '80' }]}>
                  多路复用已禁用
                </Text>
              )}
            </View>
          )}


      </ScrollView>

      {/* 保存按钮 */}
      <View style={[styles.footer, { backgroundColor, borderTopColor: borderColor }]}>
        <Button onPress={handleSave} style={styles.saveButton}>
          <Text style={styles.saveButtonText}>
            {isEditMode ? '更新配置' : '保存配置'}
          </Text>
        </Button>
      </View>

      {/* 多路复用设置底部弹窗 */}
      <BottomSheetModal
        ref={muxBottomSheetRef}
        index={1}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        backgroundStyle={{ backgroundColor }}
        handleIndicatorStyle={{ backgroundColor: borderColor }}
        enablePanDownToClose={true}
        enableOverDrag={false}
        containerComponent={WindowOverlay}
      >
        <BottomSheetScrollView style={[styles.bottomSheetContent, { backgroundColor }]}>
          <Text style={[styles.bottomSheetTitle, { color: textColor }]}>多路复用设置</Text>

          <View style={styles.row}>
            <View style={[styles.inputGroup, styles.flex1]}>
              <Label style={[styles.label, { color: textColor }]}>并发数</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={muxConcurrency}
                onChangeText={setMuxConcurrency}
                placeholder="8"
                keyboardType="numeric"
              />
            </View>
            <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
              <Label style={[styles.label, { color: textColor }]}>XUDP 并发数</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={muxXudpConcurrency}
                onChangeText={setMuxXudpConcurrency}
                placeholder="16"
                keyboardType="numeric"
              />
            </View>
          </View>

          <View>
            <Label style={[styles.label, { color: textColor }]}>XUDP 代理 UDP 443</Label>
            <Select value={{ value: muxXudpProxyUDP443, label: muxXudpProxyUDP443 === 'reject' ? '拒绝' : muxXudpProxyUDP443 === 'allow' ? '允许' : '跳过' }} onValueChange={(option) => setMuxXudpProxyUDP443(option?.value || 'reject')}>
              <SelectTrigger style={[styles.input, { borderColor }]}>
                <SelectValue placeholder="选择处理方式" className="text-foreground text-sm native:text-lg" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="reject" label="拒绝">拒绝</SelectItem>
                <SelectItem value="allow" label="允许">允许</SelectItem>
                <SelectItem value="skip" label="跳过">跳过</SelectItem>
              </SelectContent>
            </Select>
          </View>
        </BottomSheetScrollView>
      </BottomSheetModal>

      {/* Fragment 设置底部弹窗 */}
      <BottomSheetModal
        ref={fragmentBottomSheetRef}
        index={1}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        backgroundStyle={{ backgroundColor }}
        handleIndicatorStyle={{ backgroundColor: borderColor }}
        enablePanDownToClose={true}
        enableOverDrag={false}
        containerComponent={WindowOverlay}
      >
        <BottomSheetScrollView style={[styles.bottomSheetContent, { backgroundColor }]}>
          <Text style={[styles.bottomSheetTitle, { color: textColor }]}>Fragment 设置</Text>

          <View style={styles.inputGroup}>
            <Label style={[styles.label, { color: textColor }]}>数据包类型</Label>
            <Select value={{ value: fragmentPackets, label: fragmentPackets }} onValueChange={(option) => setFragmentPackets(option?.value || 'tlshello')}>
              <SelectTrigger style={[styles.input, { borderColor }]}>
                <SelectValue placeholder="选择数据包类型" className="text-foreground text-sm native:text-lg" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="tlshello" label="tlshello">tlshello</SelectItem>
                <SelectItem value="1-3" label="1-3">1-3</SelectItem>
                <SelectItem value="1-5" label="1-5">1-5</SelectItem>
              </SelectContent>
            </Select>
          </View>

          <View style={styles.row}>
            <View style={[styles.inputGroup, styles.flex1]}>
              <Label style={[styles.label, { color: textColor }]}>长度范围</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={fragmentLength}
                onChangeText={setFragmentLength}
                placeholder="100-200"
              />
            </View>
            <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
              <Label style={[styles.label, { color: textColor }]}>间隔 (ms)</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={fragmentInterval}
                onChangeText={setFragmentInterval}
                placeholder="10-20"
              />
            </View>
          </View>
        </BottomSheetScrollView>
      </BottomSheetModal>

      {/* 传输设置底部弹窗 */}
      <BottomSheetModal
        ref={transportBottomSheetRef}
        index={1}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        backgroundStyle={{ backgroundColor }}
        handleIndicatorStyle={{ backgroundColor: borderColor }}
        enablePanDownToClose={true}
        enableOverDrag={false}
        containerComponent={WindowOverlay}
      >
        <BottomSheetScrollView style={[styles.bottomSheetContent, { backgroundColor }]}>
          <Text style={[styles.bottomSheetTitle, { color: textColor }]}>传输设置</Text>

          {/* 传输方式选择 */}
          <View style={styles.section}>
            <Label style={[styles.label, { color: textColor }]}>传输方式</Label>
            <Select value={{ value: transportType, label: transportType.toUpperCase() }} onValueChange={(option) => setTransportType(option?.value || 'tcp')}>
              <SelectTrigger style={[styles.input, { borderColor }]} className='w-[250px]'>
                <SelectValue placeholder="选择传输方式" className="text-foreground text-sm native:text-lg" />
              </SelectTrigger>
              <SelectContent className='w-[250px]'>
                <SelectItem value="tcp" label="TCP (Raw)">TCP (Raw)</SelectItem>
                <SelectItem value="xhttp" label="XHTTP">XHTTP</SelectItem>
                <SelectItem value="kcp" label="KCP">KCP</SelectItem>
                <SelectItem value="grpc" label="gRPC">gRPC</SelectItem>
                <SelectItem value="websocket" label="WebSocket">WebSocket</SelectItem>
                <SelectItem value="httpupgrade" label="HTTP Upgrade">HTTP Upgrade</SelectItem>
              </SelectContent>
            </Select>
          </View>

          {/* TCP 设置 */}
          {transportType === 'tcp' && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>TCP 设置</Text>

              <View style={styles.section}>
                <View style={styles.switchRowCompact}>
                  <Switch
                    checked={tcpHeaderType === 'http'}
                    onCheckedChange={(checked) => setTcpHeaderType(checked ? 'http' : 'none')}
                  />
                  <Label style={[styles.labelRight, { color: textColor }]}>HTTP 伪装</Label>
                </View>
              </View>

              {tcpHeaderType === 'http' && (
                <>
                  <View style={styles.row}>
                    <View style={[styles.inputGroup, styles.flex1]}>
                      <Label style={[styles.label, { color: textColor }]}>请求版本</Label>
                      <Input
                        style={[styles.input, { borderColor }]}
                        value={tcpHttpRequestVersion}
                        onChangeText={setTcpHttpRequestVersion}
                        placeholder="1.1"
                      />
                    </View>
                    <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                      <Label style={[styles.label, { color: textColor }]}>请求方法</Label>
                      <Input
                        style={[styles.input, { borderColor }]}
                        value={tcpHttpRequestMethod}
                        onChangeText={setTcpHttpRequestMethod}
                        placeholder="GET"
                      />
                    </View>
                  </View>

                  <View style={styles.inputGroup}>
                    <Label style={[styles.label, { color: textColor }]}>请求路径</Label>
                    <Input
                      style={[styles.input, { borderColor }]}
                      value={tcpHttpPaths.join(',')}
                      onChangeText={(text) => setTcpHttpPaths(text.split(',').map(p => p.trim()))}
                      placeholder="/,/video"
                    />
                  </View>

                  <View style={styles.row}>
                    <View style={[styles.inputGroup, styles.flex1]}>
                      <Label style={[styles.label, { color: textColor }]}>响应版本</Label>
                      <Input
                        style={[styles.input, { borderColor }]}
                        value={tcpHttpResponseVersion}
                        onChangeText={setTcpHttpResponseVersion}
                        placeholder="1.1"
                      />
                    </View>
                    <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                      <Label style={[styles.label, { color: textColor }]}>响应状态</Label>
                      <Input
                        style={[styles.input, { borderColor }]}
                        value={tcpHttpResponseStatus}
                        onChangeText={setTcpHttpResponseStatus}
                        placeholder="200"
                      />
                    </View>
                  </View>

                  <View style={styles.inputGroup}>
                    <Label style={[styles.label, { color: textColor }]}>响应原因</Label>
                    <Input
                      style={[styles.input, { borderColor }]}
                      value={tcpHttpResponseReason}
                      onChangeText={setTcpHttpResponseReason}
                      placeholder="OK"
                    />
                  </View>

                  {/* 请求头设置 */}
                  <View style={styles.section}>
                    <View style={styles.labelWithButton}>
                      <Label style={{ color: textColor }}>请求头</Label>
                      <TouchableOpacity onPress={handleAddTcpRequestHeader}>
                        <PlusCircle size={16} color={textColor} />
                      </TouchableOpacity>
                    </View>
                    {tcpHttpRequestHeaders.map((header, index) => (
                      <View key={index} style={styles.headerItem}>
                        <View style={styles.headerKeyRow}>
                          <Input
                            style={[styles.headerKeyInput, styles.input, { borderColor }]}
                            value={header.key}
                            onChangeText={(value) => handleUpdateTcpRequestHeader(index, 'key', value)}
                            placeholder="Host"
                          />
                          <Input
                            style={[styles.headerValueInput, styles.input, { borderColor }]}
                            value={header.value}
                            onChangeText={(value) => handleUpdateTcpRequestHeader(index, 'value', value)}
                            placeholder="值"
                          />
                          <TouchableOpacity
                            style={styles.pathButton}
                            onPress={() => handleRemoveTcpRequestHeader(index)}
                          >
                            <Trash2 size={16} color="#ef4444" />
                          </TouchableOpacity>
                        </View>
                      </View>
                    ))}
                  </View>

                  {/* 响应头设置 */}
                  <View style={styles.section}>
                    <View style={styles.labelWithButton}>
                      <Label style={{ color: textColor }}>响应头</Label>
                      <TouchableOpacity onPress={handleAddTcpResponseHeader}>
                        <PlusCircle size={16} color={textColor} />
                      </TouchableOpacity>
                    </View>
                    {tcpHttpResponseHeaders.map((header, index) => (
                      <View key={index} style={styles.headerItem}>
                        <View style={styles.headerKeyRow}>
                          <Input
                            style={[styles.headerKeyInput, styles.input, { borderColor }]}
                            value={header.key}
                            onChangeText={(value) => handleUpdateTcpResponseHeader(index, 'key', value)}
                            placeholder="Content-Type"
                          />
                          <Input
                            style={[styles.headerValueInput, styles.input, { borderColor }]}
                            value={header.value}
                            onChangeText={(value) => handleUpdateTcpResponseHeader(index, 'value', value)}
                            placeholder="值"
                          />
                          <TouchableOpacity
                            style={styles.pathButton}
                            onPress={() => handleRemoveTcpResponseHeader(index)}
                          >
                            <Trash2 size={16} color="#ef4444" />
                          </TouchableOpacity>
                        </View>
                      </View>
                    ))}
                  </View>
                </>
              )}
            </View>
          )}

          {/* XHTTP 设置 */}
          {transportType === 'xhttp' && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>XHTTP 设置</Text>

              <View style={styles.row}>
                <View style={[styles.inputGroup, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>主机</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={xhttpHost}
                    onChangeText={setXhttpHost}
                    placeholder="example.com"
                  />
                </View>
                <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>路径</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={xhttpPath}
                    onChangeText={setXhttpPath}
                    placeholder="/yourpath"
                  />
                </View>
              </View>

              <View style={styles.row}>
                <View style={[styles.inputGroup, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>模式</Label>
                  <Select value={{ value: xhttpMode, label: xhttpMode }} onValueChange={(option) => setXhttpMode(option?.value || 'auto')}>
                    <SelectTrigger style={[styles.input, { borderColor }]}>
                      <SelectValue placeholder="选择模式" className="text-foreground text-sm native:text-lg" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="auto" label="auto">auto</SelectItem>
                      <SelectItem value="packet-up" label="packet-up">packet-up</SelectItem>
                      <SelectItem value="stream-up" label="stream-up">stream-up</SelectItem>
                      <SelectItem value="stream-one" label="stream-one">stream-one</SelectItem>
                    </SelectContent>
                  </Select>
                </View>
                <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>填充字节</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={xhttpXPaddingBytes}
                    onChangeText={setXhttpXPaddingBytes}
                    placeholder="100-1000"
                  />
                </View>
              </View>

              <View style={styles.section}>
                <View style={styles.switchRowCompact}>
                  <Switch
                    checked={xhttpNoGRPCHeader}
                    onCheckedChange={setXhttpNoGRPCHeader}
                  />
                  <Label style={[styles.labelRight, { color: textColor }]}>禁用 gRPC Header</Label>
                </View>
              </View>

              <View style={styles.inputGroup}>
                <Label style={[styles.label, { color: textColor }]}>最小发送间隔 (ms)</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={xhttpScMinPostsIntervalMs}
                  onChangeText={setXhttpScMinPostsIntervalMs}
                  placeholder="30"
                  keyboardType="numeric"
                />
              </View>

              {/* Headers 管理 */}
              <View style={styles.section}>
                <View style={styles.labelWithButton}>
                  <Label style={{ color: textColor }}>自定义 Headers</Label>
                  <TouchableOpacity onPress={handleAddXhttpHeader}>
                    <PlusCircle size={16} color={textColor} />
                  </TouchableOpacity>
                </View>
                {xhttpHeaders.map((header, index) => (
                  <View key={index} style={styles.headerItem}>
                    <View style={styles.headerKeyRow}>
                      <Input
                        style={[styles.headerKeyInput, styles.input, { borderColor }]}
                        value={header.key}
                        onChangeText={(value) => handleUpdateXhttpHeader(index, 'key', value)}
                        placeholder="Header Key"
                      />
                      <Input
                        style={[styles.headerValueInput, styles.input, { borderColor }]}
                        value={header.value}
                        onChangeText={(value) => handleUpdateXhttpHeader(index, 'value', value)}
                        placeholder="Header Value"
                      />
                      <TouchableOpacity
                        style={styles.pathButton}
                        onPress={() => handleRemoveXhttpHeader(index)}
                      >
                        <Trash2 size={16} color="#ef4444" />
                      </TouchableOpacity>
                    </View>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* mKCP 设置 */}
          {transportType === 'kcp' && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>mKCP 设置</Text>

              <View style={styles.row}>
                <View style={[styles.inputGroup, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>MTU</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={mkcpMtu}
                    onChangeText={setMkcpMtu}
                    placeholder="1350"
                    keyboardType="numeric"
                  />
                </View>
                <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>TTI</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={mkcpTti}
                    onChangeText={setMkcpTti}
                    placeholder="20"
                    keyboardType="numeric"
                  />
                </View>
              </View>

              <View style={styles.row}>
                <View style={[styles.inputGroup, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>上行容量</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={mkcpUplinkCapacity}
                    onChangeText={setMkcpUplinkCapacity}
                    placeholder="5"
                    keyboardType="numeric"
                  />
                </View>
                <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>下行容量</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={mkcpDownlinkCapacity}
                    onChangeText={setMkcpDownlinkCapacity}
                    placeholder="20"
                    keyboardType="numeric"
                  />
                </View>
              </View>

              <View style={styles.row}>
                <View style={[styles.inputGroup, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>读缓冲区</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={mkcpReadBufferSize}
                    onChangeText={setMkcpReadBufferSize}
                    placeholder="1"
                    keyboardType="numeric"
                  />
                </View>
                <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>写缓冲区</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={mkcpWriteBufferSize}
                    onChangeText={setMkcpWriteBufferSize}
                    placeholder="1"
                    keyboardType="numeric"
                  />
                </View>
              </View>

              <View style={styles.section}>
                <View style={styles.switchRowCompact}>
                  <Switch
                    checked={mkcpCongestion}
                    onCheckedChange={setMkcpCongestion}
                  />
                  <Label style={[styles.labelRight, { color: textColor }]}>启用拥塞控制</Label>
                </View>
              </View>

              <View style={styles.row}>
                <View style={[styles.inputGroup, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>伪装类型</Label>
                  <Select value={{ value: mkcpHeaderType, label: mkcpHeaderType }} onValueChange={(option) => setMkcpHeaderType(option?.value || 'none')}>
                    <SelectTrigger style={[styles.input, { borderColor }]}>
                      <SelectValue placeholder="选择伪装类型" className="text-foreground text-sm native:text-lg" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none" label="none">none</SelectItem>
                      <SelectItem value="srtp" label="srtp">srtp</SelectItem>
                      <SelectItem value="utp" label="utp">utp</SelectItem>
                      <SelectItem value="wechat-video" label="wechat-video">wechat-video</SelectItem>
                      <SelectItem value="dtls" label="dtls">dtls</SelectItem>
                      <SelectItem value="wireguard" label="wireguard">wireguard</SelectItem>
                    </SelectContent>
                  </Select>
                </View>
                <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>伪装域名</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={mkcpHeaderDomain}
                    onChangeText={setMkcpHeaderDomain}
                    placeholder="example.com"
                  />
                </View>
              </View>

              <View style={styles.inputGroup}>
                <Label style={[styles.label, { color: textColor }]}>种子密钥</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={mkcpSeed}
                  onChangeText={setMkcpSeed}
                  placeholder="Password"
                />
              </View>
            </View>
          )}

          {/* gRPC 设置 */}
          {transportType === 'grpc' && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>gRPC 设置</Text>

              <View style={styles.row}>
                <View style={[styles.inputGroup, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>Authority</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={grpcAuthority}
                    onChangeText={setGrpcAuthority}
                    placeholder="grpc.example.com"
                  />
                </View>
                <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>服务名</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={grpcServiceName}
                    onChangeText={setGrpcServiceName}
                    placeholder="name"
                  />
                </View>
              </View>

              <View style={styles.inputGroup}>
                <Label style={[styles.label, { color: textColor }]}>用户代理</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={grpcUserAgent}
                  onChangeText={setGrpcUserAgent}
                  placeholder="custom user agent"
                />
              </View>

              <View style={styles.section}>
                <View style={styles.switchRowCompact}>
                  <Switch
                    checked={grpcMultiMode}
                    onCheckedChange={setGrpcMultiMode}
                  />
                  <Label style={[styles.labelRight, { color: textColor }]}>multiMode</Label>
                </View>
              </View>
              <View style={styles.section}>
                <View style={styles.switchRowCompact}>
                  <Switch
                    checked={grpcPermitWithoutStream}
                    onCheckedChange={setGrpcPermitWithoutStream}
                  />
                  <Label style={[styles.labelRight, { color: textColor }]}>允许无流</Label>
                </View>
              </View>

              <View style={styles.row}>
                <View style={[styles.inputGroup, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>空闲超时 (s)</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={grpcIdleTimeout}
                    onChangeText={setGrpcIdleTimeout}
                    placeholder="60"
                    keyboardType="numeric"
                  />
                </View>
                <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>健康检查超时 (s)</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={grpcHealthCheckTimeout}
                    onChangeText={setGrpcHealthCheckTimeout}
                    placeholder="20"
                    keyboardType="numeric"
                  />
                </View>
              </View>

              <View style={styles.inputGroup}>
                <Label style={[styles.label, { color: textColor }]}>初始窗口大小</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={grpcInitialWindowsSize}
                  onChangeText={setGrpcInitialWindowsSize}
                  placeholder="0"
                  keyboardType="numeric"
                />
              </View>
            </View>
          )}

          {/* WebSocket 设置 */}
          {transportType === 'websocket' && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>WebSocket 设置</Text>

              <View style={styles.row}>
                <View style={[styles.inputGroup, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>主机</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={wsHost}
                    onChangeText={setWsHost}
                    placeholder="xray.com"
                  />
                </View>
                <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>路径</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={wsPath}
                    onChangeText={setWsPath}
                    placeholder="/yourpath"
                  />
                </View>
              </View>

              <View style={styles.inputGroup}>
                <Label style={[styles.label, { color: textColor }]}>心跳周期 (s)</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={wsHeartbeatPeriod}
                  onChangeText={setWsHeartbeatPeriod}
                  placeholder="10"
                  keyboardType="numeric"
                />
              </View>

              {/* Headers 管理 */}
              <View style={styles.section}>
                <View style={styles.labelWithButton}>
                  <Label style={{ color: textColor }}>自定义 Headers</Label>
                  <TouchableOpacity onPress={handleAddWsHeader}>
                    <PlusCircle size={16} color={textColor} />
                  </TouchableOpacity>
                </View>
                {wsHeaders.map((header, index) => (
                  <View key={index} style={styles.headerItem}>
                    <View style={styles.headerKeyRow}>
                      <Input
                        style={[styles.headerKeyInput, styles.input, { borderColor }]}
                        value={header.key}
                        onChangeText={(value) => handleUpdateWsHeader(index, 'key', value)}
                        placeholder="Header Key"
                      />
                      <Input
                        style={[styles.headerValueInput, styles.input, { borderColor }]}
                        value={header.value}
                        onChangeText={(value) => handleUpdateWsHeader(index, 'value', value)}
                        placeholder="Header Value"
                      />
                      <TouchableOpacity
                        style={styles.pathButton}
                        onPress={() => handleRemoveWsHeader(index)}
                      >
                        <Trash2 size={16} color="#ef4444" />
                      </TouchableOpacity>
                    </View>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* HTTP Upgrade 设置 */}
          {transportType === 'httpupgrade' && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle,styles.sectionHeader, { color: textColor }]}>HTTP Upgrade 设置</Text>

              <View style={styles.row}>
                <View style={[styles.inputGroup, styles.flex1]}>
                  <Label style={[styles.label, { color: textColor }]}>主机</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={httpUpgradeHost}
                    onChangeText={setHttpUpgradeHost}
                    placeholder="xray.com"
                  />
                </View>
                <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
                  <Label style={[styles.label, { color: textColor }]}>路径</Label>
                  <Input
                    style={[styles.input, { borderColor }]}
                    value={httpUpgradePath}
                    onChangeText={setHttpUpgradePath}
                    placeholder="/yourpath"
                  />
                </View>
              </View>

              {/* Headers 管理 */}
              <View style={styles.section}>
                <View style={styles.labelWithButton}>
                  <Label style={{ color: textColor }}>自定义 Headers</Label>
                  <TouchableOpacity onPress={handleAddHttpUpgradeHeader}>
                    <PlusCircle size={16} color={textColor} />
                  </TouchableOpacity>
                </View>
                {httpUpgradeHeaders.map((header, index) => (
                  <View key={index} style={styles.headerItem}>
                    <View style={styles.headerKeyRow}>
                      <Input
                        style={[styles.headerKeyInput, styles.input, { borderColor }]}
                        value={header.key}
                        onChangeText={(value) => handleUpdateHttpUpgradeHeader(index, 'key', value)}
                        placeholder="Header Key"
                      />
                      <Input
                        style={[styles.headerValueInput, styles.input, { borderColor }]}
                        value={header.value}
                        onChangeText={(value) => handleUpdateHttpUpgradeHeader(index, 'value', value)}
                        placeholder="Header Value"
                      />
                      <TouchableOpacity
                        style={styles.pathButton}
                        onPress={() => handleRemoveHttpUpgradeHeader(index)}
                      >
                        <Trash2 size={16} color="#ef4444" />
                      </TouchableOpacity>
                    </View>
                  </View>
                ))}
              </View>
            </View>
          )}
        </BottomSheetScrollView>
      </BottomSheetModal>

      {/* 安全设置底部弹窗 */}
      <BottomSheetModal
        ref={securityBottomSheetRef}
        index={1}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        backgroundStyle={{ backgroundColor }}
        handleIndicatorStyle={{ backgroundColor: borderColor }}
        enablePanDownToClose={true}
        enableOverDrag={false}
        
        containerComponent={WindowOverlay}
      >
        <BottomSheetScrollView style={[styles.bottomSheetContent, { backgroundColor }]}>
          <Text style={[styles.bottomSheetTitle, { color: textColor }]}>安全设置</Text>

          {/* 安全类型选择，参考入站表单使用 SelectionGroup */}
          <View style={styles.section}>
            <Label style={[styles.label, { color: textColor }]}>安全类型</Label>
            <SelectionGroup
              options={[
                { id: 'none', label: '无' },
                { id: 'tls', label: 'TLS' },
                { id: 'reality', label: 'Reality', disabled: !['vless','trojan'].includes(protocol) },
              ]}
              value={securityType}
              onChange={(value) => setSecurityType(value as string)}
              horizontal={true}
            />
            {protocol !== OutboundProtocols.VLESS && securityType === 'none' && (
              <Text style={[styles.placeholder, { color: textColor + '80', fontSize: 12, marginTop: 4 }]}>仅 VLESS和Trojan 协议支持 Reality</Text>
            )}
          </View>

          {/* TLS 设置 */}
          {securityType === 'tls' && (
            <>
              <Text style={[styles.sectionTitle, styles.sectionHeader, { color: textColor }]}>TLS 设置</Text>

              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>Server Name</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={tlsServerName}
                  onChangeText={setTlsServerName}
                  placeholder="example.com"
                />
              </View>

              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>指纹 (fingerprint)</Label>
                <SelectionGroup
                  options={[
                    { id: '', label: 'None' },
                    { id: 'chrome', label: 'chrome' },
                    { id: 'firefox', label: 'firefox' },
                    { id: 'safari', label: 'safari' },
                    { id: 'ios', label: 'ios' },
                    { id: 'android', label: 'android' },
                    { id: 'edge', label: 'edge' },
                    { id: '360', label: '360' },
                    { id: 'qq', label: 'qq' },
                    { id: 'random', label: 'random' },
                    { id: 'randomized', label: 'randomized' }
                  ]}
                  value={tlsFingerprint}
                  onChange={(value) => setTlsFingerprint(Array.isArray(value) ? value[0] : value)}
                  multiple={false}
                  horizontal={true}
                  buttonSize="sm"
                />
              </View>

              <View style={styles.section}>
                <View style={styles.switchRowCompact}>
                  <Switch
                    checked={tlsAllowInsecure}
                    onCheckedChange={(checked) => setTlsAllowInsecure(!!checked)}
                  />
                  <Label style={[styles.labelRight, { color: textColor }]}>允许不安全 (allowInsecure)</Label>
                </View>
              </View>

              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>ALPN</Label>
                <SelectionGroup
                  options={[{ id: 'h2', label: 'h2' }, { id: 'http/1.1', label: 'http/1.1' }, { id: 'h3', label: 'h3' }]}
                  value={tlsAlpn}
                  onChange={(vals) => setTlsAlpn(Array.isArray(vals) ? vals : [vals as string])}
                  multiple={true}
                  horizontal={true}
                />
              </View>

              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>固定对端证书SHA256 (每行一个)</Label>
                <Input
                  style={[styles.input, { borderColor, height: 100 }]}
                  value={tlsPinnedPeerCerts}
                  onChangeText={setTlsPinnedPeerCerts}
                  placeholder={"a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456\nf1e2d3c4b5a6789012345678901234567890fedcba1234567890fedcba123456"}
                  multiline
                  textAlignVertical="top"
                />
              </View>
            </>
          )}

          {/* Reality 设置 */}
          {securityType === 'reality' && (
            <>
              <Text style={[styles.sectionTitle, styles.sectionHeader, { color: textColor }]}>Reality 设置</Text>

              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>Target (Dest)</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={realityTarget}
                  onChangeText={setRealityTarget}
                  placeholder="example.com:443"
                />
              </View>

              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>Server Name</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={realityServerName}
                  onChangeText={setRealityServerName}
                  placeholder=""
                />
              </View>

              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>指纹 (fingerprint)</Label>
                <SelectionGroup
                  options={[
                    { id: '', label: 'None' },
                    { id: 'chrome', label: 'chrome' },
                    { id: 'firefox', label: 'firefox' },
                    { id: 'safari', label: 'safari' },
                    { id: 'ios', label: 'ios' },
                    { id: 'android', label: 'android' },
                    { id: 'edge', label: 'edge' },
                    { id: '360', label: '360' },
                    { id: 'qq', label: 'qq' },
                    { id: 'random', label: 'random' },
                    { id: 'randomized', label: 'randomized' }
                  ]}
                  value={realityFingerprint}
                  onChange={(value) => setRealityFingerprint(Array.isArray(value) ? value[0] : value)}
                  multiple={false}
                  horizontal={true}
                  buttonSize="sm"
                />
              </View>

              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>Password</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={realityPassword}
                  onChangeText={setRealityPassword}
                  placeholder=""
                />
              </View>

              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>Short ID</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={realityShortId}
                  onChangeText={setRealityShortId}
                  placeholder=""
                />
              </View>

              <View style={styles.section}>
                <Label style={[styles.label, { color: textColor }]}>Spider X</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={realitySpiderX}
                  onChangeText={setRealitySpiderX}
                  placeholder=""
                />
              </View>
            </>
          )}
        </BottomSheetScrollView>
      </BottomSheetModal>

      {/* SockOpt设置底部弹窗 */}
      <BottomSheetModal
        ref={sockOptBottomSheetRef}
        index={1}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        backgroundStyle={{ backgroundColor }}
        handleIndicatorStyle={{ backgroundColor: borderColor }}
        enablePanDownToClose={true}
        enableOverDrag={false}
        containerComponent={WindowOverlay}
      >
        <BottomSheetScrollView style={[styles.bottomSheetContent, { backgroundColor }]}>
          <Text style={[styles.bottomSheetTitle, { color: textColor }]}>SockOpt设置</Text>

          {/* Mark 和 TCP Max Seg 一行 */}
          <View style={styles.row}>
            <View style={[styles.section, styles.flex1]}>
              <Label style={[styles.label, { color: textColor }]}>Mark</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={sockOptMark}
                onChangeText={setSockOptMark}
                placeholder="0"
                keyboardType="numeric"
              />
            </View>
            <View style={[styles.section, styles.flex1, styles.marginLeft]}>
              <Label style={[styles.label, { color: textColor }]}>TCP Max Seg</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={sockOptTcpMaxSeg}
                onChangeText={setSockOptTcpMaxSeg}
                placeholder="1440"
                keyboardType="numeric"
              />
            </View>
          </View>

          {/* TCP Fast Open */}
          <View style={styles.bottomSheetSection}>
            <View style={styles.switchRowCompact}>
              <Switch
                checked={sockOptTcpFastOpen}
                onCheckedChange={setSockOptTcpFastOpen}
              />
              <Label style={[styles.labelRight, { color: textColor }]}>TCP Fast Open</Label>
            </View>
          </View>

          {/* TProxy 和 Domain Strategy 一行 */}
          <View style={styles.row}>
            <View style={[styles.section, styles.flex1]}>
              <Label style={[styles.label, { color: textColor }]}>TProxy</Label>
              <Select value={{ value: sockOptTproxy, label: sockOptTproxy }} onValueChange={(option) => setSockOptTproxy(option?.value || 'off')}>
                <SelectTrigger style={[styles.input, { borderColor }]}>
                  <SelectValue placeholder="选择TProxy模式" className="text-foreground text-sm native:text-lg" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="off" label="Off">Off</SelectItem>
                  <SelectItem value="redirect" label="Redirect">Redirect</SelectItem>
                  <SelectItem value="tproxy" label="TProxy">TProxy</SelectItem>
                </SelectContent>
              </Select>
            </View>
            <View style={[styles.section, styles.flex1, styles.marginLeft]}>
              <Label style={[styles.label, { color: textColor }]}>Domain Strategy</Label>
              <Select value={{ value: sockOptDomainStrategy, label: sockOptDomainStrategy }} onValueChange={(option) => setSockOptDomainStrategy(option?.value || 'AsIs')}>
                <SelectTrigger style={[styles.input, { borderColor }]}>
                  <SelectValue placeholder="选择域名策略" className="text-foreground text-sm native:text-lg" />
                </SelectTrigger>
                <SelectContent>
                  <ScrollView>
                  <SelectItem value="AsIs" label="AsIs">AsIs</SelectItem>
                  <SelectItem value="UseIP" label="UseIP">UseIP</SelectItem>
                  <SelectItem value="UseIPv6v4" label="UseIPv6v4">UseIPv6v4</SelectItem>
                  <SelectItem value="UseIPv6" label="UseIPv6">UseIPv6</SelectItem>
                  <SelectItem value="UseIPv4v6" label="UseIPv4v6">UseIPv4v6</SelectItem>
                  <SelectItem value="UseIPv4" label="UseIPv4">UseIPv4</SelectItem>
                  <SelectItem value="ForceIP" label="ForceIP">ForceIP</SelectItem>
                  <SelectItem value="ForceIPv6v4" label="ForceIPv6v4">ForceIPv6v4</SelectItem>
                  <SelectItem value="ForceIPv6" label="ForceIPv6">ForceIPv6</SelectItem>
                  <SelectItem value="ForceIPv4v6" label="ForceIPv4v6">ForceIPv4v6</SelectItem>
                  <SelectItem value="ForceIPv4" label="ForceIPv4">ForceIPv4</SelectItem>
                  </ScrollView>
                </SelectContent>
              </Select>
            </View>
          </View>

          {/* Dialer Proxy */}
          <View style={styles.bottomSheetSection}>
            <Label style={[styles.label, { color: textColor }]}>Dialer Proxy</Label>
            <Input
              style={[styles.input, { borderColor }]}
              value={sockOptDialerProxy}
              onChangeText={setSockOptDialerProxy}
              placeholder="出站代理tag"
            />
          </View>

          {/* V6 Only */}
          <View style={styles.bottomSheetSection}>
            <View style={styles.switchRowCompact}>
              <Switch
                checked={sockOptV6Only}
                onCheckedChange={setSockOptV6Only}
              />
              <Label style={[styles.labelRight, { color: textColor }]}>V6 Only</Label>
            </View>
          </View>

          {/* TCP Keep Alive Interval 和 TCP Keep Alive Idle 一行 */}
          <View style={styles.row}>
            <View style={[styles.section, styles.flex1]}>
              <Label style={[styles.label, { color: textColor }]}>TCP Keep Alive Interval</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={sockOptTcpKeepAliveInterval}
                onChangeText={setSockOptTcpKeepAliveInterval}
                placeholder="0"
                keyboardType="numeric"
              />
            </View>
            <View style={[styles.section, styles.flex1, styles.marginLeft]}>
              <Label style={[styles.label, { color: textColor }]}>TCP Keep Alive Idle</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={sockOptTcpKeepAliveIdle}
                onChangeText={setSockOptTcpKeepAliveIdle}
                placeholder="300"
                keyboardType="numeric"
              />
            </View>
          </View>

          {/* TCP User Timeout 和 TCP Congestion 一行 */}
          <View style={styles.row}>
            <View style={[styles.section, styles.flex1]}>
              <Label style={[styles.label, { color: textColor }]}>TCP User Timeout</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={sockOptTcpUserTimeout}
                onChangeText={setSockOptTcpUserTimeout}
                placeholder="10000"
                keyboardType="numeric"
              />
            </View>
            <View style={[styles.section, styles.flex1, styles.marginLeft]}>
              <Label style={[styles.label, { color: textColor }]}>TCP Congestion</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={sockOptTcpCongestion}
                onChangeText={setSockOptTcpCongestion}
                placeholder="bbr"
              />
            </View>
          </View>

          {/* Interface 和 TCP Window Clamp 一行 */}
          <View style={styles.row}>
            <View style={[styles.section, styles.flex1]}>
              <Label style={[styles.label, { color: textColor }]}>Interface</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={sockOptInterface}
                onChangeText={setSockOptInterface}
                placeholder="wg0"
              />
            </View>
            <View style={[styles.section, styles.flex1, styles.marginLeft]}>
              <Label style={[styles.label, { color: textColor }]}>TCP Window Clamp</Label>
              <Input
                style={[styles.input, { borderColor }]}
                value={sockOptTcpWindowClamp}
                onChangeText={setSockOptTcpWindowClamp}
                placeholder="600"
                keyboardType="numeric"
              />
            </View>
          </View>

          {/* Address Port Strategy */}
          <View style={styles.bottomSheetSection}>
            <Label style={[styles.label, { color: textColor }]}>Address Port Strategy</Label>
            <Select value={{ value: sockOptAddressPortStrategy, label: sockOptAddressPortStrategy || 'none' }} onValueChange={(option) => setSockOptAddressPortStrategy(option?.value || 'none')}>
              <SelectTrigger style={[styles.input, { borderColor }]}>
                <SelectValue placeholder="选择地址端口策略" className="text-foreground text-sm native:text-lg" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none" label="None">None</SelectItem>
                <SelectItem value="SrvPortOnly" label="SrvPortOnly">SrvPortOnly</SelectItem>
                <SelectItem value="SrvAddressOnly" label="SrvAddressOnly">SrvAddressOnly</SelectItem>
                <SelectItem value="SrvPortAndAddress" label="SrvPortAndAddress">SrvPortAndAddress</SelectItem>
                <SelectItem value="TxtPortOnly" label="TxtPortOnly">TxtPortOnly</SelectItem>
                <SelectItem value="TxtAddressOnly" label="TxtAddressOnly">TxtAddressOnly</SelectItem>
                <SelectItem value="TxtPortAndAddress" label="TxtPortAndAddress">TxtPortAndAddress</SelectItem>
              </SelectContent>
            </Select>
          </View>
        </BottomSheetScrollView>
      </BottomSheetModal>

      {/* Noise 编辑对话框 */}
      <Modal
        visible={showNoiseDialog}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowNoiseDialog(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor }]}>
            {/* 标题 */}
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: textColor }]}>
                {editingNoise ? '编辑 Noise' : '添加 Noise'}
              </Text>
            </View>

            <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
              {/* 类型选择 */}
              <View style={styles.dialogSection}>
                <Label style={[styles.label, { color: textColor }]}>类型</Label>
                <Select value={{ value: noiseType, label: noiseType }} onValueChange={(option) => setNoiseType(option?.value || 'base64')}>
                  <SelectTrigger style={[styles.input, { borderColor }]}>
                    <SelectValue placeholder="选择类型" className="text-foreground text-sm native:text-lg" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="rand" label="rand">rand (随机数据)</SelectItem>
                    <SelectItem value="str" label="str">str (用户自定义字符串)</SelectItem>
                    <SelectItem value="base64" label="base64">base64 (base64编码的二进制数据)</SelectItem>
                    <SelectItem value="hex" label="hex">hex (十六进制数据)</SelectItem>
                  </SelectContent>
                </Select>
              </View>

              {/* 数据包内容 */}
              <View style={styles.dialogSection}>
                <Label style={[styles.label, { color: textColor }]}>数据包内容</Label>
                <Input
                  style={[styles.input, { borderColor, height: 80 }]}
                  value={noisePacket}
                  onChangeText={setNoisePacket}
                  placeholder="输入数据包内容"
                  multiline
                  textAlignVertical="top"
                />
              </View>

              {/* 延迟设置 */}
              <View style={styles.dialogSection}>
                <Label style={[styles.label, { color: textColor }]}>延迟 (ms)</Label>
                <Input
                  style={[styles.input, { borderColor }]}
                  value={noiseDelay}
                  onChangeText={setNoiseDelay}
                  placeholder="10-16"
                />
              </View>
            </ScrollView>

            <View style={styles.modalButtons}>
              <Button variant="outline" onPress={() => setShowNoiseDialog(false)}>
                <Text>取消</Text>
              </Button>
              <Button onPress={handleSaveNoise}>
                <Text>保存</Text>
              </Button>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
    paddingBottom: 36,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  row: {
    flexDirection: 'row',
  },
  inputGroup: {
    marginBottom: 16,
  },
  flex1: {
    flex: 1,
  },
  marginLeft: {
    marginLeft: 12,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  switchRowCompact: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  labelRight: {
    fontSize: 14,
    fontWeight: '500',
  },
  sockOptControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  disabledButton: {
    opacity: 0.5,
  },
  editButtonText: {
    fontSize: 12,
  },
  transportInfo: {
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    gap: 4,
  },
  transportType: {
    fontSize: 14,
    fontWeight: '500',
  },
  transportDetail: {
    fontSize: 12,
  },
  userList: {
    gap: 8,
  },
  userCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
  },
  userInfo: {
    flex: 1,
  },
  userEmail: {
    fontSize: 14,
    fontWeight: '500',
  },
  userActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    paddingHorizontal: 4,
  },
  placeholder: {
    fontSize: 14,
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 13,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    borderTopWidth: 1,
  },
  saveButton: {
    width: '100%',
  },
  saveButtonText: {
    fontWeight: '600',
  },
  bottomSheetContent: {
    padding: 16,
  },
  bottomSheetTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
    borderRadius: 12,
    padding: 0,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalHeader: {
    padding: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  modalBody: {
    padding: 20,
    maxHeight: 400,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
    padding: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  dialogSection: {
    gap: 8,
    marginBottom: 16,
  },
  labelWithButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  headerItem: {
    marginBottom: 8,
  },
  headerKeyRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerKeyInput: {
    flex: 1,
  },
  headerValueInput: {
    flex: 1,
  },
  pathButton: {
    padding: 8,
  },
  bottomSheetSection: {
    marginBottom: 16,
  },
});