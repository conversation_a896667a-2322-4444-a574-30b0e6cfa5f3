import React from "react";
import { View } from "react-native";
import { Skeleton } from "./ui/skeleton";
import { useThemeColor } from "~/hooks/useThemeColor";

export function InboundSkeletonCard() {
    const backgroundColor = useThemeColor({}, 'background');
    const borderColor = useThemeColor({}, 'border');
    
    return (
        <View className="border-b p-3" style={{ backgroundColor, borderColor }}>
            {/* 标题行骨架 */}
            <View className="flex-row justify-between items-center mb-3">
                <View className="flex-1">
                    <View className="flex-row items-center mb-1">
                        <Skeleton className="w-32 h-5 mr-2" />
                        <Skeleton className="w-16 h-5 rounded-xl" />
                    </View>
                    <Skeleton className="w-24 h-4" />
                </View>
                <Skeleton className="w-12 h-6 rounded-xl" />
            </View>

            {/* 配置信息骨架 */}
            <View className="flex-row justify-between items-center mb-2">
                <Skeleton className="w-20 h-4" />
                <Skeleton className="w-16 h-4" />
            </View>

            {/* 流量统计骨架 */}
            <View className="flex-row justify-between items-center">
                <View className="flex-row items-center">
                    <Skeleton className="w-4 h-4 mr-1" />
                    <Skeleton className="w-16 h-4" />
                </View>
                <View className="flex-row items-center">
                    <Skeleton className="w-4 h-4 mr-1" />
                    <Skeleton className="w-16 h-4" />
                </View>
            </View>
        </View>
    );
}
