import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Badge } from '@/components/ui/badge';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useAppStore } from '@/lib/store';
import { InboundConfig } from '@/panels/3x-ui/types';
import { ThreeXUIConfig } from '@/lib/types';
import { getThreeXUIInboundList } from '@/panels/3x-ui/utils';
import { smartFetch } from '@/lib/utils';
import InboundCard from '@/panels/3x-ui/components/InboundCard';
import ExportLinksModal from '@/panels/3x-ui/components/ExportLinksModal';
import { BottomSheetModal, BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { router, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { Plus, ArrowUp, ArrowDown } from 'lucide-react-native';
import * as Clipboard from 'expo-clipboard';
import React, { useCallback, useRef, useState, useMemo } from 'react';
import { SafeAreaView, StyleSheet, View, ScrollView, Alert, ActivityIndicator } from 'react-native';

export default function InboundsScreen() {
  const { configId } = useLocalSearchParams<{ configId: string }>();
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');
  const borderColor = useThemeColor({}, 'border');

  const { configs, getServerConfig } = useAppStore();

  // 状态管理
  const [loading, setLoading] = useState(true);
  const [selectedInbound, setSelectedInbound] = useState<InboundConfig | null>(null);
  const [showExportModal, setShowExportModal] = useState(false);

  // Refs
  const actionSheetRef = useRef<BottomSheetModal>(null);

  // BottomSheet 配置
  const snapPoints = useMemo(() => ['50%'], []);

  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    []
  );

  // 获取当前配置
  const currentConfig = configs.find(c => c.id === configId) as ThreeXUIConfig;

  // 从serverConfig获取入站列表
  const serverConfig = getServerConfig(configId || '');
  const inbounds = serverConfig?.inbounds || [];

  // 加载入站列表
  const loadInbounds = useCallback(async () => {
    if (!currentConfig) return;

    try {
      setLoading(true);
      const result = await getThreeXUIInboundList(currentConfig);
      // 数据已经在getThreeXUIInboundList中存储到serverConfig了，这里不需要额外操作
    } catch (error) {
      console.error('Load inbounds failed:', error);
      Alert.alert('错误', '加载入站列表失败');
    } finally {
      setLoading(false);
    }
  }, [currentConfig]);



  // 页面聚焦时加载数据
  useFocusEffect(
    useCallback(() => {
      loadInbounds();
    }, [loadInbounds])
  );

  const handleAddConfig = () => {
    if (!configId) return;
    router.push({
      pathname: '/3x-ui/inbound-config',
      params: { configId }
    });
  };

  const handleEditConfig = (inbound: InboundConfig) => {
    if (!configId) return;
    router.push({
      pathname: '/3x-ui/inbound-config',
      params: { configId, id: inbound.tag }
    });
  };

  // 格式化流量显示
  const formatTraffic = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 处理卡片点击
  const handleCardPress = (inbound: InboundConfig) => {
    setSelectedInbound(inbound);
    actionSheetRef.current?.present();
  };

  // 处理启用/禁用切换
  const handleToggleEnable = async (enabled: boolean) => {
    if (!selectedInbound || !currentConfig) return;

    try {
      // 构造完整的入站配置对象，只修改 enable 属性
      const updatedInbound = {
        ...selectedInbound,
        enable: enabled,
        clientStats: undefined // 删除客户端统计
      };

      const formData = new FormData();

      // 遍历配置对象，添加到表单
      Object.entries(updatedInbound).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (typeof value === 'object') {
            formData.append(key, JSON.stringify(value));
          } else {
            formData.append(key, String(value));
          }
        }
      });

      const response = await smartFetch(
        `${currentConfig.protocol}://${currentConfig.url}/panel/inbound/update/${selectedInbound.id}`,
        {
          method: 'POST',
          body: formData,
        },
        currentConfig
      );

      const result = await response.json();

      if (result.success) {
        // 重新获取入站列表以更新数据
        await getThreeXUIInboundList(currentConfig);
        Alert.alert('成功', enabled ? '已启用配置' : '已禁用配置');
        actionSheetRef.current?.dismiss();
      } else {
        Alert.alert('错误', result.msg || '操作失败');
      }
    } catch (error) {
      Alert.alert('错误', '操作失败');
    }
  };

  // 处理导出链接
  const handleExportLinks = () => {
    actionSheetRef.current?.dismiss();
    setShowExportModal(true);
  };

  // 处理重置流量确认
  const handleResetTrafficConfirm = () => {
    Alert.alert(
      '确认重置',
      '确定要重置此入站配置的流量统计吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '重置',
          style: 'destructive',
          onPress: handleResetTraffic
        }
      ]
    );
  };

  // 处理重置流量
  const handleResetTraffic = async () => {
    if (!selectedInbound || !currentConfig) return;

    try {
      // 构造完整的入站配置对象，只修改流量属性
      const updatedInbound = {
        ...selectedInbound,
        up: 0,
        down: 0,
        clientStats: undefined // 删除客户端统计
      };

      const formData = new FormData();

      // 遍历配置对象，添加到表单
      Object.entries(updatedInbound).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (typeof value === 'object') {
            formData.append(key, JSON.stringify(value));
          } else {
            formData.append(key, String(value));
          }
        }
      });

      const response = await smartFetch(
        `${currentConfig.protocol}://${currentConfig.url}/panel/inbound/update/${selectedInbound.id}`,
        {
          method: 'POST',
          body: formData,
        },
        currentConfig
      );

      const result = await response.json();

      if (result.success) {
        // 重新获取入站列表以更新数据
        await getThreeXUIInboundList(currentConfig);
        Alert.alert('成功', '流量已重置');
        actionSheetRef.current?.dismiss();
      } else {
        Alert.alert('错误', result.msg || '重置失败');
      }
    } catch (error) {
      Alert.alert('错误', '重置失败');
    }
  };

  // 处理导出JSON
  const handleExportJson = async () => {
    if (!selectedInbound) return;

    try {
      const jsonString = JSON.stringify(selectedInbound, null, 2);
      await Clipboard.setStringAsync(jsonString);
      Alert.alert('成功', '配置JSON已复制到剪贴板');
      actionSheetRef.current?.dismiss();
    } catch (error) {
      Alert.alert('错误', '导出失败');
    }
  };

  // 处理删除确认
  const handleDeleteConfirm = () => {
    Alert.alert(
      '确认删除',
      '确定要删除此入站配置吗？此操作不可撤销。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: handleDelete
        }
      ]
    );
  };

  // 处理删除配置
  const handleDelete = async () => {
    if (!selectedInbound || !currentConfig) return;

    try {
      const response = await smartFetch(
        `${currentConfig.protocol}://${currentConfig.url}/panel/inbound/del/${selectedInbound.id}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          credentials: 'include'
        },
        currentConfig
      );

      const result = await response.json();

      if (result.success) {
        // 重新获取入站列表以更新数据
        await getThreeXUIInboundList(currentConfig);
        Alert.alert('成功', '配置已删除');
        actionSheetRef.current?.dismiss();
      } else {
        Alert.alert('错误', result.msg || '删除失败');
      }
    } catch (error) {
      Alert.alert('错误', '删除失败');
    }
  };

  if (!currentConfig) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: textColor }]}>
            配置不存在
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <>
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      {/* 头部按钮 */}
      <View style={[styles.header, { borderBottomColor: borderColor }]}>
        <Button onPress={handleAddConfig} style={styles.addButton}>
          <Plus size={20} color="white" />
          <Text style={styles.addButtonText}>添加配置</Text>
        </Button>
      </View>

      {/* 入站列表 */}
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" />
            <Text style={[styles.loadingText, { color: textColor + '80' }]}>
              加载中...
            </Text>
          </View>
        ) : inbounds.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyTitle, { color: textColor }]}>
              暂无入站配置
            </Text>
            <Text style={[styles.emptySubtitle, { color: textColor + '80' }]}>
              点击上方按钮添加第一个入站配置
            </Text>
          </View>
        ) : (
          <View>
            {/* 顶部横线 */}
            <View style={[styles.topDivider, { backgroundColor: borderColor }]} />
            {inbounds.map((inbound: InboundConfig, index: number) => (
              <InboundCard
                key={inbound.id || index}
                inbound={inbound}
                onPress={() => handleCardPress(inbound)}
              />
            ))}
          </View>
        )}
      </ScrollView>

      {/* 操作底部弹窗 */}
      <BottomSheetModal
        ref={actionSheetRef}
        index={0}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        backgroundStyle={{ backgroundColor }}
        handleIndicatorStyle={{ backgroundColor: borderColor }}
        enablePanDownToClose={true}
        enableOverDrag={false}
      >
        <BottomSheetView style={[actionSheetStyles.content, { backgroundColor }]}>
          {selectedInbound && (
            <>
              {/* Header */}
              <View style={actionSheetStyles.header}>
                <View style={actionSheetStyles.titleContainer}>
                  <Text style={[actionSheetStyles.headerTitle, { color: textColor }]}>
                    {selectedInbound.tag || `${selectedInbound.protocol}-${selectedInbound.port}`}
                  </Text>
                  <Badge variant="default" style={actionSheetStyles.protocolBadge}>
                    <Text style={actionSheetStyles.protocolText}>{selectedInbound.protocol}</Text>
                  </Badge>
                </View>

                {/* 流量信息 */}
                {(selectedInbound.up !== undefined || selectedInbound.down !== undefined) && (
                  <View style={actionSheetStyles.trafficContainer}>
                    <View style={actionSheetStyles.trafficItem}>
                      <ArrowUp size={16} color={textColor + '80'} />
                      <Text style={[actionSheetStyles.trafficValue, { color: textColor }]}>
                        {formatTraffic(selectedInbound.up || 0)}
                      </Text>
                    </View>
                    <View style={actionSheetStyles.trafficItem}>
                      <ArrowDown size={16} color={textColor + '80'} />
                      <Text style={[actionSheetStyles.trafficValue, { color: textColor }]}>
                        {formatTraffic(selectedInbound.down || 0)}
                      </Text>
                    </View>
                  </View>
                )}
              </View>

              {/* Header分割线 */}
              <View style={[actionSheetStyles.headerDivider, { backgroundColor: borderColor }]} />

              <View style={actionSheetStyles.buttonsContainer}>
                <Button
                  variant="ghost"
                  onPress={() => {
                    actionSheetRef.current?.dismiss();
                    if (selectedInbound) handleEditConfig(selectedInbound);
                  }}
                  size='lg'
                >
                  <Text style={[actionSheetStyles.buttonText, { color: textColor }]}>编辑配置</Text>
                </Button>

                <View style={[actionSheetStyles.divider, { backgroundColor: borderColor }]} />

                <Button
                  variant="ghost"
                  onPress={handleExportLinks}
                  size='lg'
                >
                  <Text style={[actionSheetStyles.buttonText, { color: textColor }]}>导出链接</Text>
                </Button>

                <View style={[actionSheetStyles.divider, { backgroundColor: borderColor }]} />

                <Button
                  variant="ghost"
                  onPress={handleResetTrafficConfirm}
                  size='lg'
                >
                  <Text style={[actionSheetStyles.buttonText, { color: textColor }]}>重置流量</Text>
                </Button>

                <View style={[actionSheetStyles.divider, { backgroundColor: borderColor }]} />

                <Button
                  variant="ghost"
                  onPress={handleExportJson}
                  size='lg'
                >
                  <Text style={[actionSheetStyles.buttonText, { color: textColor }]}>导出JSON</Text>
                </Button>

                {selectedInbound.enable !== undefined && (
                  <>
                    <View style={[actionSheetStyles.divider, { backgroundColor: borderColor }]} />
                    <Button
                      variant="ghost"
                      onPress={() => handleToggleEnable(!selectedInbound.enable)}
                      size='lg'
                    >
                      <Text style={[actionSheetStyles.buttonText, { color: textColor }]}>
                        {selectedInbound.enable ? '禁用' : '启用'}
                      </Text>
                    </Button>
                  </>
                )}

                <View style={[actionSheetStyles.divider, { backgroundColor: borderColor }]} />

                <Button
                  variant="ghost"
                  onPress={handleDeleteConfirm}
                  size='lg'
                >
                  <Text style={[actionSheetStyles.buttonText, actionSheetStyles.deleteButtonText]}>删除配置</Text>
                </Button>
              </View>
            </>
          )}
        </BottomSheetView>
      </BottomSheetModal>

      {/* 导出链接模态框 */}
      <ExportLinksModal
        visible={showExportModal}
        onClose={() => setShowExportModal(false)}
        inbound={selectedInbound}
        serverHost={new URL(`${currentConfig.protocol}://${currentConfig.url}`).hostname}
      />
    </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  addButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    fontSize: 14,
    marginTop: 12,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  topDivider: {
    height: 1,
  },
});

const actionSheetStyles = StyleSheet.create({
  content: {
    flex: 1,
  },
  header: {
    padding: 12,
    alignItems: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    marginBottom: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  protocolBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  protocolText: {
    fontSize: 12,
    fontWeight: '500',
    color: 'white',
  },
  trafficContainer: {
    flexDirection: 'row',
    width: '100%',
  },
  trafficItem: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
  },
  trafficValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  headerDivider: {
    height: 1,
  },
  buttonsContainer: {
    flex: 1,
    justifyContent: 'space-around',
  },
  divider: {
    height: 1,
  },
  actionButton: {
    paddingVertical: 20,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  deleteButtonText: {
    color: '#ef4444',
  },
});
